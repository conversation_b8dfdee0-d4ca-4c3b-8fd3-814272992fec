# Design Document

## Overview

The multi-vendor beauty platform will be built as a traditional server-side rendered PHP application with a clean, modular structure. The system will use a three-tier architecture with presentation (HTML/CSS/JS), business logic (PHP), and data persistence (MySQL) layers. The design emphasizes simplicity, security, and maintainability while avoiding external dependencies.

## Architecture

### System Architecture

```mermaid
graph TB
    A[Web Browser] --> B[Apache/Nginx Web Server]
    B --> C[PHP Application Layer]
    C --> D[MySQL Database]
    
    subgraph "PHP Application Layer"
        E[Public Pages]
        F[Authentication System]
        G[Business Logic]
        H[Database Access Layer]
    end
    
    C --> E
    C --> F
    C --> G
    C --> H
```

### Directory Structure

```
/
├── index.php                 # Homepage with search
├── shop.php                  # Shop profile pages
├── booking.php               # Appointment booking
├── login.php                 # User authentication
├── register.php              # User registration
├── dashboard/
│   ├── customer.php          # Customer dashboard
│   ├── shop-owner.php        # Shop owner dashboard
│   └── admin.php             # Admin panel
├── api/
│   ├── search.php            # AJAX search endpoints
│   ├── booking.php           # Booking operations
│   └── availability.php      # Check availability
├── includes/
│   ├── config.php            # Database configuration
│   ├── auth.php              # Authentication functions
│   ├── database.php          # Database connection
│   └── functions.php         # Utility functions
├── assets/
│   ├── css/
│   │   └── style.css         # Main stylesheet
│   ├── js/
│   │   └── main.js           # JavaScript functionality
│   └── images/               # Static images
└── uploads/                  # User uploaded content
```

## Components and Interfaces

### Authentication System

**Session Management:**
- PHP sessions for user state management
- Secure session configuration with httponly and secure flags
- Session timeout and regeneration for security

**Password Security:**
- PHP `password_hash()` with PASSWORD_DEFAULT algorithm
- Password verification using `password_verify()`
- Minimum password requirements (8+ characters, mixed case, numbers)

**User Roles:**
- Customer: Basic user with booking capabilities
- Shop Owner: Business account with shop management
- Admin: Full platform management access

### Database Access Layer

**Connection Management:**
- PDO with prepared statements for all queries
- Connection pooling through persistent connections
- Error handling with try-catch blocks

**Query Builder Pattern:**
```php
class DatabaseQuery {
    public function select($table, $conditions = [], $joins = [])
    public function insert($table, $data)
    public function update($table, $data, $conditions)
    public function delete($table, $conditions)
}
```

### Business Logic Components

**Shop Management:**
- Shop registration and approval workflow
- Branch management with location data
- Service catalog with pricing
- Staff scheduling and availability

**Booking System:**
- Real-time availability checking
- Appointment scheduling with conflict prevention
- Booking confirmation and notifications
- Cancellation and rescheduling logic

**Search and Filtering:**
- Location-based search with city/country filters
- Service type filtering
- Rating and price range filters
- Pagination for large result sets

## Data Models

### Database Schema

```sql
-- Users table (customers, shop owners, admins)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('customer', 'shop_owner', 'admin') NOT NULL,
    status ENUM('active', 'suspended', 'pending') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Shops table
CREATE TABLE shops (
    id INT PRIMARY KEY AUTO_INCREMENT,
    owner_id INT NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    description TEXT,
    status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id)
);

-- Shop branches table
CREATE TABLE shop_branches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shop_id INT NOT NULL,
    branch_name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    working_hours JSON,
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (shop_id) REFERENCES shops(id)
);

-- Services table
CREATE TABLE services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    branch_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    duration_minutes INT NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    category VARCHAR(100),
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (branch_id) REFERENCES shop_branches(id)
);

-- Staff table
CREATE TABLE staff (
    id INT PRIMARY KEY AUTO_INCREMENT,
    branch_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    specialties JSON,
    working_hours JSON,
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (branch_id) REFERENCES shop_branches(id)
);

-- Appointments table
CREATE TABLE appointments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    service_id INT NOT NULL,
    staff_id INT,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration_minutes INT NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (service_id) REFERENCES services(id),
    FOREIGN KEY (staff_id) REFERENCES staff(id)
);

-- Reviews table
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    shop_id INT NOT NULL,
    appointment_id INT,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    status ENUM('active', 'hidden', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (shop_id) REFERENCES shops(id),
    FOREIGN KEY (appointment_id) REFERENCES appointments(id)
);
```

### Data Relationships

- Users can be customers, shop owners, or admins
- Shop owners can have multiple shops
- Shops can have multiple branches in different locations
- Each branch offers multiple services
- Services can be performed by specific staff members
- Customers can book appointments for services
- Customers can leave reviews for shops after appointments

## Error Handling

### PHP Error Management

**Error Logging:**
- All errors logged to dedicated log files
- Separate logs for application errors, database errors, and security events
- Log rotation to prevent disk space issues

**User-Friendly Error Pages:**
- Custom 404 page for missing resources
- Generic error page for system failures
- Maintenance mode capability

**Validation and Sanitization:**
- Input validation on both client and server side
- HTML entity encoding for output
- SQL injection prevention through prepared statements
- CSRF protection for form submissions

### Database Error Handling

**Connection Failures:**
- Graceful degradation when database is unavailable
- Retry logic for temporary connection issues
- Fallback to cached data where appropriate

**Query Failures:**
- Transaction rollback for data consistency
- Detailed error logging for debugging
- User notification without exposing system details

## Testing Strategy

### Manual Testing Approach

**User Acceptance Testing:**
- Test all user workflows (registration, booking, management)
- Cross-browser compatibility testing
- Mobile responsiveness testing
- Performance testing under load

**Security Testing:**
- Authentication and authorization testing
- Input validation and SQL injection testing
- Session management security testing
- File upload security testing

**Integration Testing:**
- Database integration testing
- Email notification testing
- Payment processing testing (if implemented)
- Third-party service integration testing

### Code Quality Assurance

**Code Standards:**
- Consistent PHP coding standards (PSR-12 style)
- Meaningful variable and function names
- Comprehensive code comments
- Modular, reusable code structure

**Database Testing:**
- Data integrity constraints testing
- Performance testing for complex queries
- Backup and recovery testing
- Migration testing for schema changes

### Deployment and Monitoring

**Production Readiness:**
- Environment-specific configuration files
- Database migration scripts
- Asset optimization (CSS/JS minification)
- Security headers and SSL configuration

**Monitoring:**
- Application performance monitoring
- Database query performance tracking
- Error rate monitoring
- User activity analytics

## User Interface Design

### Design System

**Color Palette:**
- Primary: Soft Rose Pink (#F8C1C1)
- Accent: Lavender Gray (#C1C8E4)
- Background: Ivory/White (#FFFFFF)
- Dark Text: Charcoal Gray (#333333)
- Light Text: Cool Gray (#777777)
- Buttons/Links: Deep Plum (#6D2E82)
- Success: Emerald Green (#38A169)

**Typography:**
- Clean, readable fonts (system fonts for performance)
- Consistent heading hierarchy
- Adequate line spacing and contrast

**Layout Principles:**
- Mobile-first responsive design
- Clean, minimal interface with soft edges
- Consistent spacing and alignment
- Intuitive navigation patterns

### Page Templates

**Homepage:**
- Hero section with search functionality
- Featured shops grid
- Category filters
- Location-based recommendations

**Shop Profile:**
- Shop header with basic info and rating
- Tabbed interface (Overview, Services, Reviews, Booking)
- Service listing with prices and booking buttons
- Review section with ratings display

**Dashboard Interfaces:**
- Clean, card-based layouts
- Quick action buttons
- Data tables with sorting and filtering
- Mobile-optimized navigation