<?php
/**
 * Authentication System Foundation
 * Handles user authentication, session management, and registration
 */

require_once 'config.php';
require_once 'database.php';
require_once 'functions.php';

class AuthSystem {
    private $db;
    
    public function __construct() {
        $this->db = new DatabaseQuery();
    }
    
    /**
     * Secure Password Hashing Functions
     */
    
    /**
     * Hash password using secure algorithm
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify password against hash
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Check if password needs rehashing (for security updates)
     */
    public function needsRehash($hash) {
        return password_needs_rehash($hash, PASSWORD_DEFAULT);
    }
    
    /**
     * Session Management with Security Configurations
     */
    
    /**
     * Initialize secure session
     */
    public function initializeSession() {
        // Session security settings are already configured in config.php
        
        // Check if session is expired
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity'] > SESSION_LIFETIME)) {
            $this->destroySession();
            return false;
        }
        
        // Update last activity time
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * Create user session after successful authentication
     */
    public function createUserSession($user) {
        // Regenerate session ID for security
        session_regenerate_id(true);
        
        // Store user information in session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['user_status'] = $user['status'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        // Log successful login
        logSecurityEvent('user_login', [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role']
        ]);
        
        return true;
    }
    
    /**
     * Destroy user session
     */
    public function destroySession() {
        // Log logout if user was logged in
        if (isset($_SESSION['user_id'])) {
            logSecurityEvent('user_logout', [
                'user_id' => $_SESSION['user_id'],
                'session_duration' => time() - ($_SESSION['login_time'] ?? time())
            ]);
        }
        
        // Clear all session data
        $_SESSION = [];
        
        // Destroy session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Destroy session
        session_destroy();
        
        return true;
    }
    
    /**
     * Check if user is authenticated
     */
    public function isAuthenticated() {
        return isset($_SESSION['user_id']) && 
               isset($_SESSION['user_status']) && 
               $_SESSION['user_status'] === 'active';
    }
    
    /**
     * Check if user has specific role
     */
    public function hasRole($role) {
        return $this->isAuthenticated() && 
               isset($_SESSION['user_role']) && 
               $_SESSION['user_role'] === $role;
    }
    
    /**
     * Get current user information
     */
    public function getCurrentUser() {
        if (!$this->isAuthenticated()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'email' => $_SESSION['user_email'],
            'role' => $_SESSION['user_role'],
            'name' => $_SESSION['user_name'],
            'status' => $_SESSION['user_status']
        ];
    }
    
    /**
     * User Registration Functionality with Validation
     */
    
    /**
     * Register new user with validation
     */
    public function registerUser($userData) {
        try {
            // Validate input data
            $validationResult = $this->validateRegistrationData($userData);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'errors' => $validationResult['errors']
                ];
            }
            
            // Check if email already exists
            if ($this->emailExists($userData['email'])) {
                return [
                    'success' => false,
                    'errors' => ['email' => 'Email address is already registered']
                ];
            }
            
            // Sanitize input data
            $sanitizedData = $this->sanitizeRegistrationData($userData);
            
            // Hash password
            $sanitizedData['password_hash'] = $this->hashPassword($sanitizedData['password']);
            unset($sanitizedData['password']);
            unset($sanitizedData['confirm_password']);
            
            // Set default values
            $sanitizedData['status'] = 'active';
            $sanitizedData['created_at'] = date('Y-m-d H:i:s');
            $sanitizedData['updated_at'] = date('Y-m-d H:i:s');
            
            // Insert user into database
            $userId = $this->db->insert('users', $sanitizedData);
            
            if ($userId) {
                // Log successful registration
                logSecurityEvent('user_registration', [
                    'user_id' => $userId,
                    'email' => $sanitizedData['email'],
                    'role' => $sanitizedData['role']
                ]);
                
                return [
                    'success' => true,
                    'user_id' => $userId,
                    'message' => 'Registration successful'
                ];
            } else {
                return [
                    'success' => false,
                    'errors' => ['general' => 'Registration failed. Please try again.']
                ];
            }
            
        } catch (Exception $e) {
            logError('Registration error: ' . $e->getMessage(), $userData);
            return [
                'success' => false,
                'errors' => ['general' => 'Registration failed. Please try again.']
            ];
        }
    }
    
    /**
     * Validate registration data
     */
    private function validateRegistrationData($data) {
        $errors = [];
        
        // Validate email
        if (empty($data['email'])) {
            $errors['email'] = 'Email is required';
        } elseif (!validateEmail($data['email'])) {
            $errors['email'] = 'Please enter a valid email address';
        }
        
        // Validate password
        if (empty($data['password'])) {
            $errors['password'] = 'Password is required';
        } elseif (!validatePassword($data['password'])) {
            $errors['password'] = 'Password must be at least 8 characters with uppercase, lowercase, and number';
        }
        
        // Validate password confirmation
        if (empty($data['confirm_password'])) {
            $errors['confirm_password'] = 'Password confirmation is required';
        } elseif ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'Passwords do not match';
        }
        
        // Validate first name
        if (empty($data['first_name'])) {
            $errors['first_name'] = 'First name is required';
        } elseif (!validateName($data['first_name'])) {
            $errors['first_name'] = 'Please enter a valid first name';
        }
        
        // Validate last name
        if (empty($data['last_name'])) {
            $errors['last_name'] = 'Last name is required';
        } elseif (!validateName($data['last_name'])) {
            $errors['last_name'] = 'Please enter a valid last name';
        }
        
        // Validate phone (optional but if provided must be valid)
        if (!empty($data['phone']) && !validatePhone($data['phone'])) {
            $errors['phone'] = 'Please enter a valid phone number';
        }
        
        // Validate role
        $allowedRoles = ['customer', 'shop_owner'];
        if (empty($data['role'])) {
            $errors['role'] = 'User role is required';
        } elseif (!in_array($data['role'], $allowedRoles)) {
            $errors['role'] = 'Invalid user role selected';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Sanitize registration data
     */
    private function sanitizeRegistrationData($data) {
        return [
            'email' => sanitizeEmail($data['email']),
            'password' => $data['password'], // Will be hashed, don't sanitize
            'confirm_password' => $data['confirm_password'], // Will be removed
            'first_name' => sanitizeString($data['first_name']),
            'last_name' => sanitizeString($data['last_name']),
            'phone' => !empty($data['phone']) ? sanitizePhone($data['phone']) : null,
            'role' => sanitizeString($data['role'])
        ];
    }
    
    /**
     * Check if email already exists
     */
    private function emailExists($email) {
        return $this->db->exists('users', ['email' => $email]);
    }
    
    /**
     * User Authentication Functions
     */
    
    /**
     * Authenticate user login
     */
    public function authenticateUser($email, $password) {
        try {
            // Validate input
            if (empty($email) || empty($password)) {
                return [
                    'success' => false,
                    'message' => 'Email and password are required'
                ];
            }
            
            if (!validateEmail($email)) {
                return [
                    'success' => false,
                    'message' => 'Please enter a valid email address'
                ];
            }
            
            // Find user by email
            $user = $this->db->selectOne('users', ['email' => sanitizeEmail($email)]);
            
            if (!$user) {
                // Log failed login attempt
                logSecurityEvent('login_failed', [
                    'email' => $email,
                    'reason' => 'user_not_found'
                ]);
                
                return [
                    'success' => false,
                    'message' => 'Invalid email or password'
                ];
            }
            
            // Check if user account is active
            if ($user['status'] !== 'active') {
                logSecurityEvent('login_failed', [
                    'user_id' => $user['id'],
                    'email' => $email,
                    'reason' => 'account_not_active',
                    'status' => $user['status']
                ]);
                
                $statusMessage = $user['status'] === 'suspended' ? 
                    'Your account has been suspended' : 
                    'Your account is pending approval';
                
                return [
                    'success' => false,
                    'message' => $statusMessage
                ];
            }
            
            // Verify password
            if (!$this->verifyPassword($password, $user['password_hash'])) {
                logSecurityEvent('login_failed', [
                    'user_id' => $user['id'],
                    'email' => $email,
                    'reason' => 'invalid_password'
                ]);
                
                return [
                    'success' => false,
                    'message' => 'Invalid email or password'
                ];
            }
            
            // Check if password needs rehashing
            if ($this->needsRehash($user['password_hash'])) {
                $newHash = $this->hashPassword($password);
                $this->db->update('users', 
                    ['password_hash' => $newHash], 
                    ['id' => $user['id']]
                );
            }
            
            // Create user session
            $this->createUserSession($user);
            
            return [
                'success' => true,
                'user' => $user,
                'message' => 'Login successful'
            ];
            
        } catch (Exception $e) {
            logError('Authentication error: ' . $e->getMessage(), ['email' => $email]);
            return [
                'success' => false,
                'message' => 'Authentication failed. Please try again.'
            ];
        }
    }
    
    /**
     * Logout user
     */
    public function logoutUser() {
        $this->destroySession();
        return [
            'success' => true,
            'message' => 'Logged out successfully'
        ];
    }
    
    /**
     * Security Helper Functions
     */
    
    /**
     * Generate and validate CSRF tokens
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
            $_SESSION[CSRF_TOKEN_NAME] = generateSecureToken(32);
        }
        return $_SESSION[CSRF_TOKEN_NAME];
    }
    
    public function verifyCSRFToken($token) {
        if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
            return false;
        }
        return hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
    }
    
    /**
     * Rate limiting for login attempts
     */
    public function checkLoginRateLimit($email) {
        $cacheKey = 'login_attempts_' . md5($email);
        
        if (!isset($_SESSION[$cacheKey])) {
            $_SESSION[$cacheKey] = [
                'attempts' => 0,
                'last_attempt' => time()
            ];
        }
        
        $attempts = $_SESSION[$cacheKey];
        
        // Reset attempts if more than 15 minutes have passed
        if (time() - $attempts['last_attempt'] > 900) {
            $_SESSION[$cacheKey] = [
                'attempts' => 0,
                'last_attempt' => time()
            ];
            return true;
        }
        
        // Check if too many attempts
        if ($attempts['attempts'] >= 5) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Record failed login attempt
     */
    public function recordFailedLogin($email) {
        $cacheKey = 'login_attempts_' . md5($email);
        
        if (!isset($_SESSION[$cacheKey])) {
            $_SESSION[$cacheKey] = [
                'attempts' => 0,
                'last_attempt' => time()
            ];
        }
        
        $_SESSION[$cacheKey]['attempts']++;
        $_SESSION[$cacheKey]['last_attempt'] = time();
    }
    
    /**
     * Clear login attempts after successful login
     */
    public function clearLoginAttempts($email) {
        $cacheKey = 'login_attempts_' . md5($email);
        unset($_SESSION[$cacheKey]);
    }
    
    /**
     * Require authentication for protected pages
     */
    public function requireAuth($redirectUrl = '/login.php') {
        if (!$this->isAuthenticated()) {
            $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
            redirect($redirectUrl);
        }
    }
    
    /**
     * Require specific role for protected pages
     */
    public function requireRole($role, $redirectUrl = '/') {
        $this->requireAuth();
        
        if (!$this->hasRole($role)) {
            setFlashMessage('error', 'Access denied. Insufficient permissions.');
            redirect($redirectUrl);
        }
    }
    
    /**
     * Get user by ID
     */
    public function getUserById($userId) {
        try {
            return $this->db->selectOne('users', ['id' => $userId]);
        } catch (Exception $e) {
            logError('Get user by ID error: ' . $e->getMessage(), ['user_id' => $userId]);
            return null;
        }
    }
    
    /**
     * Update user last login time
     */
    public function updateLastLogin($userId) {
        try {
            $this->db->update('users', 
                ['updated_at' => date('Y-m-d H:i:s')], 
                ['id' => $userId]
            );
        } catch (Exception $e) {
            logError('Update last login error: ' . $e->getMessage(), ['user_id' => $userId]);
        }
    }
}

// Global authentication instance
$auth = new AuthSystem();

// Initialize session
$auth->initializeSession();

/**
 * Global helper functions for authentication
 */

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    global $auth;
    return $auth->isAuthenticated();
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    global $auth;
    return $auth->hasRole($role);
}

/**
 * Get current user
 */
function getCurrentUser() {
    global $auth;
    return $auth->getCurrentUser();
}

/**
 * Require authentication
 */
function requireAuth($redirectUrl = '/login.php') {
    global $auth;
    $auth->requireAuth($redirectUrl);
}

/**
 * Require specific role
 */
function requireRole($role, $redirectUrl = '/') {
    global $auth;
    $auth->requireRole($role, $redirectUrl);
}

/**
 * Generate CSRF token for forms
 */
function csrfToken() {
    global $auth;
    return $auth->generateCSRFToken();
}

/**
 * Verify CSRF token
 */
function verifyCsrfToken($token) {
    global $auth;
    return $auth->verifyCSRFToken($token);
}

?>