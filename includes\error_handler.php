<?php
/**
 * Enhanced Error Handling System
 * Provides comprehensive error logging and user-friendly error pages
 */

require_once 'config.php';
require_once 'functions.php';

class ErrorHandler
{
    private static $instance = null;
    private $logPath;
    private $errorCounts = [];
    
    private function __construct()
    {
        $this->logPath = __DIR__ . '/../logs/';
        $this->ensureLogDirectory();
        $this->registerHandlers();
    }
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Register error and exception handlers
     */
    private function registerHandlers()
    {
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleFatalError']);
    }
    
    /**
     * Ensure log directory exists
     */
    private function ensureLogDirectory()
    {
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }
    
    /**
     * Handle PHP errors
     */
    public function handleError($errno, $errstr, $errfile, $errline)
    {
        // Don't handle errors that are suppressed with @
        if (!(error_reporting() & $errno)) {
            return false;
        }
        
        $errorTypes = [
            E_ERROR => 'Fatal Error',
            E_WARNING => 'Warning',
            E_PARSE => 'Parse Error',
            E_NOTICE => 'Notice',
            E_CORE_ERROR => 'Core Error',
            E_CORE_WARNING => 'Core Warning',
            E_COMPILE_ERROR => 'Compile Error',
            E_COMPILE_WARNING => 'Compile Warning',
            E_USER_ERROR => 'User Error',
            E_USER_WARNING => 'User Warning',
            E_USER_NOTICE => 'User Notice',
            E_STRICT => 'Strict Notice',
            E_RECOVERABLE_ERROR => 'Recoverable Error',
            E_DEPRECATED => 'Deprecated',
            E_USER_DEPRECATED => 'User Deprecated'
        ];
        
        $errorType = $errorTypes[$errno] ?? 'Unknown Error';
        
        // Create error context
        $context = [
            'type' => $errorType,
            'errno' => $errno,
            'file' => $errfile,
            'line' => $errline,
            'message' => $errstr,
            'user_id' => getCurrentUserId(),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
        
        // Log the error
        $this->logError($context);
        
        // Track error frequency
        $this->trackErrorFrequency($errfile, $errline);
        
        // For fatal errors, show error page
        if (in_array($errno, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
            $this->showErrorPage(500, 'Internal Server Error');
            exit;
        }
        
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public function handleException($exception)
    {
        $context = [
            'type' => 'Exception',
            'class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'user_id' => getCurrentUserId(),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
        
        $this->logError($context);
        $this->showErrorPage(500, 'Internal Server Error');
    }
    
    /**
     * Handle fatal errors
     */
    public function handleFatalError()
    {
        $error = error_get_last();
        
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $context = [
                'type' => 'Fatal Error',
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'user_id' => getCurrentUserId(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
                'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
                'timestamp' => date('Y-m-d H:i:s'),
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true)
            ];
            
            $this->logError($context);
            
            // Only show error page if headers haven't been sent
            if (!headers_sent()) {
                $this->showErrorPage(500, 'Internal Server Error');
            }
        }
    }
    
    /**
     * Log error to file
     */
    private function logError($context)
    {
        $logFile = $this->logPath . 'error.log';
        
        // Create structured log entry
        $logEntry = [
            'timestamp' => $context['timestamp'],
            'level' => $this->getLogLevel($context['type']),
            'type' => $context['type'],
            'message' => $context['message'],
            'file' => $context['file'],
            'line' => $context['line'],
            'user_id' => $context['user_id'],
            'ip' => $context['ip'],
            'request_uri' => $context['request_uri'],
            'request_method' => $context['request_method'],
            'memory_usage' => $this->formatBytes($context['memory_usage']),
            'peak_memory' => $this->formatBytes($context['peak_memory'])
        ];
        
        // Add trace for exceptions
        if (isset($context['trace'])) {
            $logEntry['trace'] = $context['trace'];
        }
        
        // Write to log file
        $logLine = json_encode($logEntry, JSON_UNESCAPED_SLASHES) . PHP_EOL;
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        
        // Rotate log if needed
        $this->rotateLogIfNeeded($logFile);
        
        // Send critical errors to security log
        if (in_array($context['type'], ['Fatal Error', 'Exception', 'Parse Error'])) {
            $this->logSecurityEvent('critical_error', $context);
        }
    }
    
    /**
     * Get log level for error type
     */
    private function getLogLevel($errorType)
    {
        $levels = [
            'Fatal Error' => 'CRITICAL',
            'Exception' => 'ERROR',
            'Parse Error' => 'CRITICAL',
            'Warning' => 'WARNING',
            'Notice' => 'INFO',
            'Deprecated' => 'WARNING'
        ];
        
        return $levels[$errorType] ?? 'ERROR';
    }
    
    /**
     * Track error frequency to detect patterns
     */
    private function trackErrorFrequency($file, $line)
    {
        $key = md5($file . ':' . $line);
        
        if (!isset($this->errorCounts[$key])) {
            $this->errorCounts[$key] = [
                'count' => 0,
                'first_seen' => time(),
                'last_seen' => time(),
                'file' => $file,
                'line' => $line
            ];
        }
        
        $this->errorCounts[$key]['count']++;
        $this->errorCounts[$key]['last_seen'] = time();
        
        // Alert if error occurs frequently
        if ($this->errorCounts[$key]['count'] > 10 && 
            (time() - $this->errorCounts[$key]['first_seen']) < 300) { // 10 errors in 5 minutes
            
            $this->logSecurityEvent('frequent_error', [
                'file' => $file,
                'line' => $line,
                'count' => $this->errorCounts[$key]['count'],
                'time_span' => time() - $this->errorCounts[$key]['first_seen']
            ]);
        }
    }
    
    /**
     * Log security events
     */
    private function logSecurityEvent($event, $details)
    {
        $securityLog = $this->logPath . 'security.log';
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'user_id' => getCurrentUserId(),
            'details' => $details
        ];
        
        $logLine = json_encode($logData, JSON_UNESCAPED_SLASHES) . PHP_EOL;
        file_put_contents($securityLog, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Rotate log file if it gets too large
     */
    private function rotateLogIfNeeded($logFile)
    {
        if (!file_exists($logFile)) {
            return;
        }
        
        // Rotate if file is larger than 10MB
        if (filesize($logFile) > 10 * 1024 * 1024) {
            $rotatedFile = $logFile . '.' . date('Y-m-d_H-i-s');
            rename($logFile, $rotatedFile);
            
            // Keep only last 10 rotated files
            $pattern = dirname($logFile) . '/' . basename($logFile) . '.*';
            $rotatedFiles = glob($pattern);
            
            if (count($rotatedFiles) > 10) {
                usort($rotatedFiles, function($a, $b) {
                    return filemtime($a) - filemtime($b);
                });
                
                // Remove oldest files
                for ($i = 0; $i < count($rotatedFiles) - 10; $i++) {
                    unlink($rotatedFiles[$i]);
                }
            }
        }
    }
    
    /**
     * Show user-friendly error page
     */
    private function showErrorPage($code = 500, $message = 'Internal Server Error')
    {
        if (headers_sent()) {
            return;
        }
        
        http_response_code($code);
        
        $errorPages = [
            404 => '404.php',
            500 => '500.php'
        ];
        
        $errorPage = $errorPages[$code] ?? '500.php';
        $errorPagePath = __DIR__ . '/../' . $errorPage;
        
        if (file_exists($errorPagePath)) {
            include $errorPagePath;
        } else {
            // Fallback error page
            $this->showFallbackErrorPage($code, $message);
        }
    }
    
    /**
     * Show fallback error page when custom error pages are not available
     */
    private function showFallbackErrorPage($code, $message)
    {
        echo "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Error {$code} - Beauty Platform</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            text-align: center; 
            margin: 0; 
            padding: 50px 20px; 
            background-color: #f5f5f5; 
        }
        .error-container { 
            max-width: 500px; 
            margin: 0 auto; 
            background: white; 
            padding: 40px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .error-icon { 
            font-size: 64px; 
            margin-bottom: 20px; 
        }
        h1 { 
            color: #e74c3c; 
            margin-bottom: 20px; 
        }
        p { 
            color: #666; 
            margin-bottom: 30px; 
            line-height: 1.6; 
        }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #6D2E82; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            transition: background 0.3s; 
        }
        .btn:hover { 
            background: #5a2569; 
        }
    </style>
</head>
<body>
    <div class='error-container'>
        <div class='error-icon'>⚠️</div>
        <h1>Error {$code}</h1>
        <p>" . htmlspecialchars($message) . "</p>
        <p>We apologize for the inconvenience. Please try again later.</p>
        <a href='/' class='btn'>Return to Home</a>
    </div>
</body>
</html>";
    }
    
    /**
     * Format bytes for display
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Get error statistics
     */
    public function getErrorStats()
    {
        $errorLog = $this->logPath . 'error.log';
        
        if (!file_exists($errorLog)) {
            return [
                'total_errors' => 0,
                'recent_errors' => 0,
                'error_types' => [],
                'top_files' => []
            ];
        }
        
        $lines = file($errorLog, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $totalErrors = count($lines);
        $recentErrors = 0;
        $errorTypes = [];
        $topFiles = [];
        
        $oneDayAgo = time() - 86400;
        
        foreach (array_reverse(array_slice($lines, -1000)) as $line) { // Last 1000 entries
            $data = json_decode($line, true);
            if (!$data) continue;
            
            // Count recent errors (last 24 hours)
            if (strtotime($data['timestamp']) > $oneDayAgo) {
                $recentErrors++;
            }
            
            // Count error types
            $type = $data['type'] ?? 'Unknown';
            $errorTypes[$type] = ($errorTypes[$type] ?? 0) + 1;
            
            // Count files with errors
            $file = basename($data['file'] ?? 'unknown');
            $topFiles[$file] = ($topFiles[$file] ?? 0) + 1;
        }
        
        // Sort by frequency
        arsort($errorTypes);
        arsort($topFiles);
        
        return [
            'total_errors' => $totalErrors,
            'recent_errors' => $recentErrors,
            'error_types' => array_slice($errorTypes, 0, 10),
            'top_files' => array_slice($topFiles, 0, 10)
        ];
    }
}

// Initialize error handler
ErrorHandler::getInstance();