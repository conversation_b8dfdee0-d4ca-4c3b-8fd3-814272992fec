/* Beauty Platform CSS - Authentication Pages */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  color: #333333;
  background-color: #f8f9fa;
}

/* Authentication Page Styles */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 400px;
}

.auth-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px 30px;
  margin: 0 auto;
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h1 {
  color: #6d2e82;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.auth-header p {
  color: #777777;
  font-size: 16px;
}

.auth-header small {
  display: block;
  color: #999;
  font-size: 14px;
  margin-top: 5px;
}

/* Success and Error Icons */
.success-icon,
.error-icon {
  font-size: 48px;
  margin: 20px 0;
}

.success-icon {
  color: #38a169;
}

.error-icon {
  color: #e53e3e;
}

/* Flash Messages */
.flash-messages {
  margin-bottom: 20px;
}

.alert {
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 10px;
  font-size: 14px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Form Styles */
.auth-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333333;
  font-size: 14px;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  background-color: #ffffff;
}

.form-control:focus {
  outline: none;
  border-color: #6d2e82;
  box-shadow: 0 0 0 3px rgba(109, 46, 130, 0.1);
}

.form-control.error {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-control::placeholder {
  color: #999999;
}

/* Password Input Wrapper */
.password-input-wrapper {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6d2e82;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
}

.password-toggle:hover {
  color: #4a1f5c;
}

.password-requirements {
  margin-top: 6px;
}

.password-requirements small {
  color: #777777;
  font-size: 12px;
}

/* Error Messages */
.error-message {
  color: #e53e3e;
  font-size: 14px;
  margin-top: 6px;
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  font-size: 14px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #333333;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
}

.forgot-password-link {
  color: #6d2e82;
  text-decoration: none;
}

.forgot-password-link:hover {
  text-decoration: underline;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1.5;
}

.btn-primary {
  background-color: #6d2e82;
  color: #ffffff;
}

.btn-primary:hover {
  background-color: #4a1f5c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(109, 46, 130, 0.3);
}

.btn-secondary {
  background-color: #e1e5e9;
  color: #333333;
}

.btn-secondary:hover {
  background-color: #d1d5db;
}

.btn-full {
  width: 100%;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.form-actions .btn {
  flex: 1;
}

/* Auth Footer */
.auth-footer {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.auth-footer p {
  color: #777777;
  font-size: 14px;
}

.auth-footer a {
  color: #6d2e82;
  text-decoration: none;
  font-weight: 500;
}

.auth-footer a:hover {
  text-decoration: underline;
}

/* User Info Display */
.user-info {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.user-info p {
  margin-bottom: 8px;
  color: #777777;
  font-size: 14px;
}

.user-info strong {
  color: #333333;
  font-size: 16px;
}

.user-info small {
  color: #999999;
  font-size: 13px;
}

/* Reset Success/Error Pages */
.reset-success,
.reset-error {
  text-align: center;
}

.reset-success p,
.reset-error p {
  margin-bottom: 16px;
  color: #555555;
  line-height: 1.6;
}

.reset-info {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
}

.reset-info h3 {
  color: #333333;
  font-size: 16px;
  margin-bottom: 12px;
}

.reset-info ul {
  list-style: none;
  padding: 0;
}

.reset-info li {
  padding: 4px 0;
  color: #666666;
  font-size: 14px;
}

.reset-info li:before {
  content: "•";
  color: #6d2e82;
  margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-page {
    padding: 10px;
  }

  .auth-card {
    padding: 30px 20px;
  }

  .auth-header h1 {
    font-size: 24px;
  }

  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .btn {
    width: 100%;
  }
}

/* Loading States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Focus Styles for Accessibility */
.btn:focus,
.form-control:focus,
.checkbox-label:focus-within {
  outline: 2px solid #6d2e82;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .auth-page {
    background: white;
  }

  .auth-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
/* Re
gistration Page Specific Styles */
.header-container {
  min-height: 70px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  padding: 20px;
}
.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  padding: 20px;
}

.auth-wrapper {
  width: 100%;
  max-width: 500px;
}

.auth-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px 30px;
  margin: 0 auto;
}

/* Role Selection Styles */
.role-selection {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.role-option {
  flex: 1;
}

.role-option input[type="radio"] {
  display: none;
}

.role-label {
  display: block;
  padding: 20px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  background-color: #ffffff;
}

.role-label:hover {
  border-color: #6d2e82;
  background-color: #f8f9fa;
}

.role-option input[type="radio"]:checked + .role-label {
  border-color: #6d2e82;
  background-color: rgba(109, 46, 130, 0.05);
}

.role-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.role-info h3 {
  color: #333333;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.role-info p {
  color: #777777;
  font-size: 13px;
  line-height: 1.4;
}

/* Form Row for Side-by-Side Fields */
.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

/* Input Styles */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"] {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  background-color: #ffffff;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus {
  outline: none;
  border-color: #6d2e82;
  box-shadow: 0 0 0 3px rgba(109, 46, 130, 0.1);
}

/* Validation States */
input.valid {
  border-color: #38a169;
  box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

input.invalid {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

/* Checkbox Group */
.checkbox-group {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.checkbox-group input[type="checkbox"] {
  margin-top: 3px;
  flex-shrink: 0;
}

.checkbox-group label {
  font-size: 14px;
  line-height: 1.5;
  color: #555555;
}

.checkbox-group a {
  color: #6d2e82;
  text-decoration: none;
}

.checkbox-group a:hover {
  text-decoration: underline;
}

/* Auth Links */
.auth-links {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.auth-links p {
  color: #777777;
  font-size: 14px;
}

.auth-links a {
  color: #6d2e82;
  text-decoration: none;
  font-weight: 500;
}

.auth-links a:hover {
  text-decoration: underline;
}

/* Success Message */
.success-message {
  text-align: center;
}

.success-message h3 {
  color: #38a169;
  margin-bottom: 10px;
}

.success-message p {
  color: #555555;
  margin-bottom: 20px;
}

/* Responsive Design for Registration */
@media (max-width: 600px) {
  .auth-wrapper {
    max-width: 100%;
  }

  .auth-card {
    padding: 30px 20px;
  }

  .role-selection {
    flex-direction: column;
    gap: 10px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .role-label {
    padding: 15px 12px;
  }

  .role-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }

  .role-info h3 {
    font-size: 15px;
  }

  .role-info p {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  .auth-header h1 {
    font-size: 24px;
  }

  .auth-header p {
    font-size: 14px;
  }
}
/* H
omepage Styles */

/* Header */
.header {
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  width: 100%;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.logo a {
  color: #6d2e82;
  text-decoration: none;
}

.logo a:hover {
  color: #4a1f5c;
}

.nav {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.nav-link {
  color: #333333;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background-color: #f8f9fa;
  color: #6d2e82;
}

/* Hero Section */
.hero {
  position: relative;
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  padding: 5rem 0 6rem 0;
  text-align: center;
  overflow: hidden;
}
.hero-bg-illustration {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: url('https://www.transparenttextures.com/patterns/diamond-upholstery.png'), linear-gradient(120deg, #f8c1c1 0%, #c1c8e4 100%);
  opacity: 0.12;
  z-index: 1;
  pointer-events: none;
}
.hero-content {
  position: relative;
  z-index: 2;
  max-width: 700px;
  margin: 0 auto;
  padding: 2rem 1rem 0 1rem;
}
.hero-title {
  font-size: 3.2rem;
  font-weight: 800;
  color: #2d1a3a;
  margin-bottom: 1.2rem;
  line-height: 1.1;
  letter-spacing: -1px;
}
.hero-subtitle {
  font-size: 1.35rem;
  color: #5a4a6d;
  margin-bottom: 2.2rem;
  line-height: 1.6;
}
.hero-cta {
  margin-bottom: 2.5rem;
}
.btn.btn-lg {
  font-size: 1.15rem;
  padding: 1rem 2.5rem;
  border-radius: 32px;
  box-shadow: 0 4px 16px rgba(109,46,130,0.08);
  font-weight: 600;
}
.hero-animated-icon {
  font-size: 2.5rem;
  margin-bottom: 2.5rem;
  animation: hero-bounce 2.5s infinite cubic-bezier(.68,-0.55,.27,1.55);
  display: inline-block;
}
@keyframes hero-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-18px) scale(1.12); }
}
.hero-search-form {
  margin-top: 0;
  box-shadow: 0 8px 32px rgba(109,46,130,0.10);
  border: 2px solid #fff0f6;
  background: #fff;
}
@media (max-width: 600px) {
  .hero-title { font-size: 2.1rem; }
  .hero-content { padding: 1rem 0 0 0; }
  .btn.btn-lg { font-size: 1rem; padding: 0.75rem 1.5rem; }
  .hero-animated-icon { font-size: 2rem; }
}

/* Search Form */
.search-form {
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.search-fields {
  display: flex;
  gap: 1rem;
  align-items: end;
}

.search-field {
  flex: 1;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #6d2e82;
  box-shadow: 0 0 0 3px rgba(109, 46, 130, 0.1);
}

.search-btn {
  background-color: #6d2e82;
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.search-btn:hover {
  background-color: #4a1f5c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(109, 46, 130, 0.3);
}

.search-icon {
  font-size: 18px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Main Content */
.main {
  flex: 1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Search Results */
.search-results {
  padding: 3rem 0;
}

.results-header {
  text-align: center;
  margin-bottom: 2rem;
}

.results-header h2 {
  font-size: 2rem;
  color: #333333;
  margin-bottom: 0.5rem;
}

.results-count {
  color: #777777;
  font-size: 1.1rem;
}

/* Shop Grid */
.shops-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.shop-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.shop-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.shop-card.featured {
  position: relative;
  border: 2px solid #6d2e82;
}

.featured-badge {
  position: absolute;
  top: -10px;
  right: 1rem;
  background-color: #6d2e82;
  color: #ffffff;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

.shop-header {
  margin-bottom: 1rem;
}

.shop-name {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.shop-name a {
  color: #333333;
  text-decoration: none;
  transition: color 0.3s ease;
}

.shop-name a:hover {
  color: #6d2e82;
}

.branch-name {
  color: #777777;
  font-size: 0.9rem;
  margin: 0;
}

.shop-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.location-icon {
  font-size: 16px;
}

.shop-description {
  color: #555555;
  line-height: 1.5;
  margin-bottom: 1rem;
  font-size: 0.95rem;
}

.shop-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stars {
  font-size: 16px;
  line-height: 1;
}

.rating-text {
  color: #666666;
  font-size: 0.875rem;
}

.no-rating {
  color: #999999;
  font-size: 0.875rem;
  font-style: italic;
}

.services-count {
  color: #6d2e82;
  font-size: 0.875rem;
  font-weight: 500;
}

.shop-actions {
  text-align: center;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 3rem 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.no-results-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-results h3 {
  font-size: 1.5rem;
  color: #333333;
  margin-bottom: 1rem;
}

.no-results p {
  color: #666666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.search-suggestions {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: left;
}

.search-suggestions h4 {
  color: #333333;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.search-suggestions ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.search-suggestions li {
  padding: 0.5rem 0;
  color: #555555;
  position: relative;
  padding-left: 1.5rem;
}

.search-suggestions li:before {
  content: "•";
  color: #6d2e82;
  position: absolute;
  left: 0;
}

/* Popular Cities */
.popular-cities {
  background-color: #f8f9fa;
  padding: 3rem 0;
}

.popular-cities h2 {
  text-align: center;
  font-size: 2rem;
  color: #333333;
  margin-bottom: 2rem;
}

.cities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.city-card {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  color: #333333;
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.city-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  color: #6d2e82;
}

.city-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.city-card p {
  margin: 0 0 0.5rem 0;
  color: #666666;
  font-size: 0.9rem;
}

.shop-count {
  color: #6d2e82;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Featured Shops */
.featured-shops {
  padding: 3rem 0;
}

.featured-shops h2 {
  text-align: center;
  font-size: 2rem;
  color: #333333;
  margin-bottom: 2rem;
}

/* Footer */
.footer {
  background-color: #333333;
  color: #ffffff;
  padding: 3rem 0 1rem;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1rem;
  color: #f8c1c1;
}

.footer-section p {
  color: #cccccc;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section ul li a {
  color: #cccccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #f8c1c1;
}

.footer-bottom {
  border-top: 1px solid #555555;
  padding-top: 1rem;
  text-align: center;
  color: #cccccc;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .search-form {
    padding: 1.5rem;
  }

  .search-fields {
    flex-direction: column;
    gap: 1rem;
  }

  .search-btn {
    width: 100%;
    justify-content: center;
  }

  .shops-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .cities-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .nav {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 1.75rem;
  }

  .search-form {
    padding: 1rem;
  }

  .shop-card {
    padding: 1rem;
  }

  .container {
    padding: 0 0.75rem;
  }
}
/*
 Shop Profile Page Styles */

/* Breadcrumb */
.breadcrumb {
  padding: 1rem 0;
  font-size: 0.9rem;
  color: #666666;
}

.breadcrumb a {
  color: #6d2e82;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: #999999;
}

.breadcrumb-current {
  color: #333333;
  font-weight: 500;
}

/* Shop Header */
.shop-header {
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  padding: 2rem 0;
}

.shop-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.shop-info {
  flex: 1;
}

.shop-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333333;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.branch-selector {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.branch-selector label {
  font-weight: 500;
  color: #555555;
}

.branch-selector select {
  padding: 0.5rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 0.9rem;
  background-color: #ffffff;
  cursor: pointer;
}

.branch-selector select:focus {
  outline: none;
  border-color: #6d2e82;
}

.shop-location {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.location-icon {
  font-size: 18px;
  margin-top: 0.1rem;
}

.location-details {
  flex: 1;
}

.address {
  font-weight: 500;
  color: #333333;
  margin-bottom: 0.25rem;
}

.city-country {
  color: #666666;
  font-size: 0.95rem;
}

.shop-contact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.shop-contact a {
  color: #6d2e82;
  text-decoration: none;
}

.shop-contact a:hover {
  text-decoration: underline;
}

.shop-rating {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 200px;
}

.rating-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.rating-score {
  font-size: 2.5rem;
  font-weight: 700;
  color: #6d2e82;
  line-height: 1;
}

.rating-stars {
  font-size: 20px;
  line-height: 1;
}

.rating-count {
  color: #666666;
  font-size: 0.9rem;
}

.no-rating {
  color: #999999;
}

.no-rating-text {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.be-first {
  font-size: 0.9rem;
  color: #6d2e82;
}

/* Shop Content Tabs */
.shop-content {
  padding: 3rem 0;
}

.tabs {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-nav {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.tab-btn {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  font-size: 1rem;
  font-weight: 500;
  color: #666666;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background-color: #e9ecef;
  color: #333333;
}

.tab-btn.active {
  background-color: #ffffff;
  color: #6d2e82;
  border-bottom-color: #6d2e82;
}

.tab-content {
  display: none;
  padding: 2rem;
}

.tab-content.active {
  display: block;
}

/* Overview Tab */
.overview-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.shop-description h3 {
  color: #333333;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.shop-description p {
  color: #555555;
  line-height: 1.6;
  font-size: 1rem;
}

.quick-info {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.info-item h4 {
  color: #333333;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.info-item p {
  color: #666666;
  font-size: 0.95rem;
}

.staff-section h3 {
  color: #333333;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.staff-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.staff-card {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #6d2e82;
}

.staff-card h4 {
  color: #333333;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.specialties {
  color: #666666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.specialties strong {
  color: #333333;
}

/* Services Tab */
.services-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.service-category h3 {
  color: #333333;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #6d2e82;
}

.services-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  transition: all 0.3s ease;
}

.service-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #6d2e82;
}

.service-info {
  flex: 1;
}

.service-item h4 {
  color: #333333;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.service-description {
  color: #666666;
  margin-bottom: 0.75rem;
  line-height: 1.4;
  font-size: 0.95rem;
}

.service-duration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #777777;
  font-size: 0.9rem;
}

.duration-icon {
  font-size: 14px;
}

.service-price {
  font-size: 1.25rem;
  font-weight: 600;
  color: #6d2e82;
  min-width: 80px;
  text-align: right;
}

.service-action {
  min-width: 120px;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.no-services {
  text-align: center;
  padding: 2rem;
  color: #666666;
}

/* Working Hours Tab */
.hours-content h3 {
  color: #333333;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.hours-table {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.hours-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.hours-row.today {
  background-color: rgba(109, 46, 130, 0.05);
  border-color: #6d2e82;
}

.day-name {
  font-weight: 500;
  color: #333333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.today-badge {
  background-color: #6d2e82;
  color: #ffffff;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.day-hours {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #666666;
}

.status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.open {
  background-color: #d4edda;
  color: #155724;
}

.status.closed {
  background-color: #f8d7da;
  color: #721c24;
}

.closed {
  color: #999999;
  font-style: italic;
}

/* Reviews Tab */
.reviews-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.reviews-header {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.reviews-header h3 {
  color: #333333;
  font-size: 1.25rem;
}

.rating-breakdown {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.overall-rating {
  text-align: center;
  min-width: 150px;
}

.rating-score-large {
  font-size: 3rem;
  font-weight: 700;
  color: #6d2e82;
  line-height: 1;
}

.rating-stars-large {
  font-size: 24px;
  margin: 0.5rem 0;
}

.total-reviews {
  color: #666666;
  font-size: 0.9rem;
}

.rating-bars {
  flex: 1;
  max-width: 300px;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.star-label {
  min-width: 40px;
  font-size: 0.9rem;
  color: #666666;
}

.bar-container {
  flex: 1;
  height: 8px;
  background-color: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background-color: #6d2e82;
  transition: width 0.3s ease;
}

.star-count {
  min-width: 30px;
  text-align: right;
  font-size: 0.9rem;
  color: #666666;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.review-item {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.reviewer-info {
  flex: 1;
}

.reviewer-name {
  font-weight: 500;
  color: #333333;
  margin-bottom: 0.25rem;
}

.review-date {
  color: #666666;
  font-size: 0.9rem;
}

.review-rating {
  font-size: 16px;
}

.review-comment {
  color: #555555;
  line-height: 1.6;
  font-size: 0.95rem;
}

.no-reviews {
  text-align: center;
  padding: 2rem;
  color: #666666;
}

/* Booking Tab */
.booking-content h3 {
  color: #333333;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.booking-content > p {
  color: #666666;
  margin-bottom: 2rem;
}

.booking-services {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.booking-category h4 {
  color: #333333;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e1e5e9;
}

.booking-services-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.booking-service-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  transition: all 0.3s ease;
}

.booking-service-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #6d2e82;
}

.service-details {
  flex: 1;
}

.service-details h5 {
  color: #333333;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.service-details p {
  color: #666666;
  margin-bottom: 0.75rem;
  line-height: 1.4;
  font-size: 0.9rem;
}

.service-meta {
  display: flex;
  gap: 1rem;
  color: #777777;
  font-size: 0.85rem;
}

.service-book {
  min-width: 140px;
}

/* Responsive Design for Shop Profile */
@media (max-width: 768px) {
  .shop-header-content {
    flex-direction: column;
    gap: 1.5rem;
  }

  .shop-title {
    font-size: 2rem;
  }

  .shop-rating {
    align-self: stretch;
  }

  .tab-nav {
    flex-wrap: wrap;
  }

  .tab-btn {
    flex: 1 1 auto;
    min-width: 120px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .tab-content {
    padding: 1.5rem;
  }

  .rating-breakdown {
    flex-direction: column;
    gap: 1.5rem;
  }

  .service-item,
  .booking-service-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .service-price {
    text-align: left;
    font-size: 1.1rem;
  }

  .service-action,
  .service-book {
    min-width: auto;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .staff-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .shop-header {
    padding: 1.5rem 0;
  }

  .shop-title {
    font-size: 1.75rem;
  }

  .tab-nav {
    flex-direction: column;
  }

  .tab-btn {
    flex: none;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
    border-radius: 0;
  }

  .tab-btn.active {
    border-bottom-color: #e1e5e9;
    border-left: 3px solid #6d2e82;
  }

  .tab-content {
    padding: 1rem;
  }

  .hours-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 1rem;
  }

  .review-header {
    flex-direction: column;
    gap: 0.5rem;
  }

  .branch-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .branch-selector select {
    width: 100%;
  }
}
/* Shop Reg
istration Page Styles */
.shop-register-wrapper {
  width: 100%;
  max-width: 80;
}
/* Dashbo
ard Styles */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.dashboard-nav {
  width: 280px;
  background-color: #ffffff;
  border-right: 1px solid #e1e5e9;
  padding: 20px;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
}

.dashboard-main {
  flex: 1;
  margin-left: 280px;
  padding: 20px;
  max-width: calc(100% - 280px);
}

.nav-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e1e5e9;
}

.nav-header h2 {
  color: #6d2e82;
  font-size: 20px;
  margin-bottom: 5px;
}

.nav-header p {
  color: #777777;
  font-size: 14px;
}

.nav-menu {
  list-style: none;
  margin-bottom: 30px;
}

.nav-menu li {
  margin-bottom: 5px;
}

.nav-link {
  display: block;
  padding: 12px 16px;
  color: #333333;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background-color: #f8f9fa;
  color: #6d2e82;
}

.nav-link.active {
  background-color: #6d2e82;
  color: #ffffff;
}

.nav-footer {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.dashboard-section {
  display: none;
}

.dashboard-section.active {
  display: block;
}

.section-header {
  margin-bottom: 30px;
}

.section-header h2 {
  color: #333333;
  font-size: 28px;
  margin-bottom: 8px;
}

.section-header p {
  color: #777777;
  font-size: 16px;
}

/* Welcome Card */
.welcome-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.welcome-content h3 {
  color: #6d2e82;
  font-size: 24px;
  margin-bottom: 16px;
}

.welcome-content p {
  color: #777777;
  font-size: 16px;
  margin-bottom: 30px;
  line-height: 1.6;
}

.welcome-steps {
  display: flex;
  gap: 30px;
  margin-bottom: 40px;
  justify-content: center;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  max-width: 200px;
}

.step-number {
  background-color: #6d2e82;
  color: #ffffff;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content h4 {
  color: #333333;
  font-size: 16px;
  margin-bottom: 8px;
}

.step-content p {
  color: #777777;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.welcome-actions {
  margin-top: 30px;
}

/* Shop Status Card */
.shop-status-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.shop-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e1e5e9;
}

.shop-status-header h3 {
  color: #333333;
  font-size: 22px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.approved {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge.suspended {
  background-color: #f8d7da;
  color: #721c24;
}

.status-message {
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.status-message.pending {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
}

.status-message.approved {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

.status-message.rejected {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.status-message.suspended {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.status-message h4 {
  margin-bottom: 10px;
  font-size: 18px;
}

.status-message p {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* Quick Stats */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.stat-item h4 {
  color: #6d2e82;
  font-size: 28px;
  margin-bottom: 8px;
}

.stat-item p {
  color: #777777;
  font-size: 14px;
  margin: 0;
}

/* Quick Actions */
.quick-actions {
  margin-top: 30px;
}

.quick-actions h3 {
  color: #333333;
  font-size: 20px;
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 25px;
  text-decoration: none;
  color: inherit;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 15px;
}

.action-card h4 {
  color: #333333;
  font-size: 18px;
  margin-bottom: 10px;
}

.action-card p {
  color: #777777;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* Shop Details Card */
.shop-details-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #e1e5e9;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row strong {
  color: #333333;
  font-weight: 600;
  min-width: 150px;
}

.detail-row span {
  color: #777777;
  flex: 1;
  text-align: right;
}

/* Empty State */
.empty-state {
  background: #ffffff;
  border-radius: 12px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.empty-state h3 {
  color: #333333;
  font-size: 20px;
  margin-bottom: 10px;
}

.empty-state p {
  color: #777777;
  font-size: 16px;
  line-height: 1.6;
}

/* Dashboard Stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
  color: #6d2e82;
  font-size: 32px;
  margin-bottom: 8px;
}

.stat-card p {
  color: #777777;
  font-size: 14px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-nav {
    width: 100%;
    position: relative;
    height: auto;
  }

  .dashboard-main {
    margin-left: 0;
    max-width: 100%;
  }

  .welcome-steps {
    flex-direction: column;
    gap: 20px;
  }

  .step {
    max-width: none;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .quick-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .detail-row {
    flex-direction: column;
    gap: 5px;
  }

  .detail-row span {
    text-align: left;
  }
} /* Add
itional Button Styles */
.btn-outline {
  background-color: transparent;
  color: #6d2e82;
  border: 2px solid #6d2e82;
}

.btn-outline:hover {
  background-color: #6d2e82;
  color: #ffffff;
}

.btn-info {
  background-color: #17a2b8;
  color: #ffffff;
}

.btn-info:hover {
  background-color: #138496;
}

.btn-success {
  background-color: #38a169;
  color: #ffffff;
}

.btn-success:hover {
  background-color: #2f855a;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: #ffffff;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 14px;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #ffffff;
  margin: 5% auto;
  padding: 0;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 20px 30px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  color: #333333;
  font-size: 20px;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #777777;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #333333;
}

.modal-body {
  padding: 30px;
}

.modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #e1e5e9;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Table Styles */
.shops-table {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.shops-table table {
  width: 100%;
  border-collapse: collapse;
}

.shops-table th,
.shops-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.shops-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333333;
}

.shops-table tr:hover {
  background-color: #f8f9fa;
}

/* Shop Cards Grid */
.shops-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.shop-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.shop-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.shop-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e1e5e9;
}

.shop-header h3 {
  color: #333333;
  font-size: 18px;
  margin: 0;
  flex: 1;
}

.shop-details {
  margin-bottom: 20px;
}

.shop-details .detail-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.shop-details .detail-row:last-child {
  border-bottom: none;
}

.shop-details .detail-row strong {
  color: #333333;
  font-weight: 600;
  min-width: 100px;
}

.shop-details .detail-row span,
.shop-details .detail-row p {
  color: #777777;
  margin: 0;
  flex: 1;
  text-align: right;
}

.shop-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.shop-actions form {
  display: inline;
} /* Shop
 Registration Page Styles */
.shop-register-wrapper {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  padding: 20px;
}

.shop-register-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 800px;
}

.shop-register-header {
  text-align: center;
  margin-bottom: 40px;
}

.shop-register-header h1 {
  color: #6d2e82;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 10px;
}

.shop-register-header p {
  color: #777777;
  font-size: 18px;
}

.shop-register-form {
  max-width: none;
}

.form-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e1e5e9;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h2 {
  color: #333333;
  font-size: 24px;
  margin-bottom: 15px;
}

.section-description {
  color: #777777;
  font-size: 16px;
  margin-bottom: 25px;
  line-height: 1.6;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-help {
  color: #999999;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

/* Working Hours Styles */
.working-hours-grid {
  display: grid;
  gap: 20px;
}

.working-hours-day {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.day-label {
  font-weight: 600;
  color: #333333;
  font-size: 16px;
}

.closed-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.closed-toggle input[type="checkbox"] {
  margin: 0;
}

.closed-toggle label {
  margin: 0;
  font-size: 14px;
  color: #777777;
  cursor: pointer;
}

.time-inputs {
  display: flex;
  gap: 20px;
  align-items: center;
}

.time-group {
  flex: 1;
}

.time-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.time-group input[type="time"] {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.time-group input[type="time"]:focus {
  outline: none;
  border-color: #6d2e82;
  box-shadow: 0 0 0 3px rgba(109, 46, 130, 0.1);
}

/* Checkbox Group Styles */
.checkbox-group {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.checkbox-group input[type="checkbox"] {
  margin-top: 3px;
  flex-shrink: 0;
}

.checkbox-group label {
  margin: 0;
  line-height: 1.5;
  cursor: pointer;
}

.checkbox-group a {
  color: #6d2e82;
  text-decoration: none;
}

.checkbox-group a:hover {
  text-decoration: underline;
}

/* Form Links */
.form-links {
  text-align: center;
  margin-top: 20px;
}

.form-links a {
  color: #6d2e82;
  text-decoration: none;
  font-size: 14px;
}

.form-links a:hover {
  text-decoration: underline;
}

/* Success Message Styles */
.success-message {
  text-align: center;
  padding: 40px;
}

.success-message h3 {
  color: #38a169;
  font-size: 24px;
  margin-bottom: 15px;
}

.success-message p {
  color: #777777;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 25px;
}

.success-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

/* Role Selection Styles */
.role-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 10px;
}

.role-option {
  position: relative;
}

.role-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.role-label {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.role-label:hover {
  border-color: #6d2e82;
  background-color: #f8f9fa;
}

.role-option input[type="radio"]:checked + .role-label {
  border-color: #6d2e82;
  background-color: rgba(109, 46, 130, 0.05);
}

.role-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.role-info h3 {
  color: #333333;
  font-size: 16px;
  margin-bottom: 5px;
}

.role-info p {
  color: #777777;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

/* Input Validation States */
.form-control.valid {
  border-color: #38a169;
  box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

.form-control.invalid {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

/* Responsive Design for Shop Registration */
@media (max-width: 768px) {
  .shop-register-card {
    padding: 30px 20px;
    margin: 10px;
  }

  .shop-register-header h1 {
    font-size: 28px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .role-selection {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .time-inputs {
    flex-direction: column;
    gap: 15px;
  }

  .success-actions {
    flex-direction: column;
  }

  .working-hours-day {
    padding: 15px;
  }

  .day-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .shop-register-wrapper {
    padding: 10px;
  }

  .shop-register-card {
    padding: 20px 15px;
  }

  .form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
  }

  .form-section h2 {
    font-size: 20px;
  }
} /*
 Dashboard Styles */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.dashboard-nav {
  width: 280px;
  background: #ffffff;
  border-right: 1px solid #e9ecef;
  padding: 20px 0;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
}

.nav-header {
  padding: 0 20px 20px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.nav-header h2 {
  color: #6d2e82;
  font-size: 20px;
  margin-bottom: 5px;
}

.nav-header p {
  color: #777777;
  font-size: 14px;
}

.nav-menu {
  list-style: none;
  padding: 0 20px;
}

.nav-menu li {
  margin-bottom: 5px;
}

.nav-link {
  display: block;
  padding: 12px 16px;
  color: #333333;
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background-color: #f8f9fa;
  color: #6d2e82;
}

.nav-link.active {
  background-color: #6d2e82;
  color: #ffffff;
}

.nav-footer {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
}

.nav-footer .btn {
  width: 100%;
  margin-bottom: 10px;
}

.dashboard-main {
  flex: 1;
  margin-left: 280px;
  padding: 30px;
}

.dashboard-section {
  display: none;
}

.dashboard-section.active {
  display: block;
}

.section-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.section-header h2 {
  color: #333333;
  font-size: 28px;
  margin-bottom: 5px;
}

.section-header p {
  color: #777777;
  font-size: 16px;
}

/* Cards */
.welcome-card,
.shop-status-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-bottom: 30px;
}

.welcome-content h3,
.shop-status-header h3 {
  color: #6d2e82;
  font-size: 24px;
  margin-bottom: 15px;
}

.shop-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.approved {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge.suspended {
  background-color: #f8d7da;
  color: #721c24;
}

.status-message {
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.status-message.pending {
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
}

.status-message.approved {
  background-color: #d4edda;
  border-left: 4px solid #28a745;
}

.status-message.rejected {
  background-color: #f8d7da;
  border-left: 4px solid #dc3545;
}

.status-message.suspended {
  background-color: #f8d7da;
  border-left: 4px solid #dc3545;
}

.status-message h4 {
  margin-bottom: 10px;
  color: inherit;
}

/* Quick Stats */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item h4 {
  font-size: 32px;
  color: #6d2e82;
  margin-bottom: 5px;
}

.stat-item p {
  color: #777777;
  font-size: 14px;
}

/* Quick Actions */
.quick-actions {
  margin-top: 30px;
}

.quick-actions h3 {
  color: #333333;
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  text-decoration: none;
  color: #333333;
  transition: all 0.2s ease;
}

.action-card:hover {
  border-color: #6d2e82;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(109, 46, 130, 0.1);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 15px;
}

.action-card h4 {
  color: #6d2e82;
  margin-bottom: 10px;
}

.action-card p {
  color: #777777;
  font-size: 14px;
}

/* Branch Management */
.branches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.branch-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.branch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.branch-header h3 {
  color: #6d2e82;
  margin: 0;
}

.branch-actions {
  display: flex;
  gap: 10px;
}

.branch-details p {
  margin-bottom: 10px;
}

.working-hours {
  margin-top: 15px;
}

.hours-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 5px;
  margin-top: 10px;
}

.day-hours {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #f8f9fa;
}

.day {
  font-weight: 500;
  color: #333333;
}

.hours {
  color: #777777;
}

/* Service Management */
.services-grid {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.branch-services {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.branch-title {
  color: #6d2e82;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f8f9fa;
}

.services-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.service-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.service-header h4 {
  color: #333333;
  margin: 0;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.service-description {
  color: #777777;
  font-size: 14px;
  margin-bottom: 10px;
}

.service-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 14px;
}

.service-duration,
.service-price,
.service-category {
  color: #777777;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #ffffff;
  margin: 5% auto;
  padding: 0;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  color: #6d2e82;
  margin: 0;
}

.close {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #333;
}

.modal form {
  padding: 30px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #6d2e82;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
  display: none;
}

/* Working Hours Form */
.working-hours-form {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
}

.day-hours-form {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f8f9fa;
}

.day-hours-form:last-child {
  border-bottom: none;
}

.day-label {
  width: 100px;
  font-weight: 500;
  color: #333333;
}

.hours-inputs {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15px;
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-inputs input {
  width: 120px;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #6d2e82;
  color: #ffffff;
}

.btn-primary:hover {
  background-color: #5a2569;
}

.btn-secondary {
  background-color: #6c757d;
  color: #ffffff;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-outline {
  background-color: transparent;
  color: #6d2e82;
  border: 1px solid #6d2e82;
}

.btn-outline:hover {
  background-color: #6d2e82;
  color: #ffffff;
}

.btn-danger {
  background-color: #dc3545;
  color: #ffffff;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 2px dashed #e9ecef;
}

.empty-state h3 {
  color: #6d2e82;
  margin-bottom: 15px;
}

.empty-state p {
  color: #777777;
  margin-bottom: 20px;
}

/* Shop Details */
.shop-details-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f8f9fa;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row strong {
  color: #333333;
  min-width: 150px;
}

.detail-row span {
  color: #777777;
  text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }

  .dashboard-nav {
    position: relative;
    width: 100%;
    height: auto;
  }

  .dashboard-main {
    margin-left: 0;
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .branches-grid,
  .actions-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 10% auto;
  }

  .day-hours-form {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .day-label {
    width: auto;
  }
}
/* St
aff Management Styles */
.staff-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.branch-staff {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.branch-title {
  color: #6d2e82;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #6d2e82;
}

.staff-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

.staff-card {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.staff-card:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-color: #6d2e82;
}

.staff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.staff-header h4 {
  color: #333333;
  font-size: 1.1rem;
  margin: 0;
}

.staff-actions {
  display: flex;
  gap: 0.5rem;
}

.staff-details p {
  margin-bottom: 0.75rem;
  color: #555555;
  font-size: 0.95rem;
}

.staff-details strong {
  color: #333333;
}

.staff-specialties {
  margin-bottom: 1rem;
}

.specialties-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.specialty-tag {
  background-color: #6d2e82;
  color: #ffffff;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.working-hours {
  margin-top: 1rem;
}

.working-hours strong {
  color: #333333;
  display: block;
  margin-bottom: 0.75rem;
}

.hours-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.5rem;
}

.day-hours {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #e1e5e9;
  font-size: 0.85rem;
}

.day {
  font-weight: 500;
  color: #333333;
}

.hours {
  color: #666666;
}

/* Staff Modal Styles */
.specialties-input {
  margin-top: 0.5rem;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.checkbox-group label:hover {
  background-color: #f8f9fa;
}

.checkbox-group input[type="checkbox"] {
  margin: 0;
  width: 16px;
  height: 16px;
}

/* Working Hours Form Styles */
.working-hours-form {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.day-hours-form {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.day-label {
  min-width: 80px;
  font-weight: 500;
  color: #333333;
}

.hours-inputs {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.hours-inputs label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #555555;
  cursor: pointer;
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.time-inputs input[type="time"] {
  padding: 0.5rem;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  font-size: 0.9rem;
}

.time-inputs span {
  color: #777777;
  font-size: 0.9rem;
}

/* Responsive Design for Staff Management */
@media (max-width: 768px) {
  .staff-list {
    grid-template-columns: 1fr;
  }

  .staff-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .staff-actions {
    align-self: stretch;
    justify-content: flex-end;
  }

  .hours-grid {
    grid-template-columns: 1fr;
  }

  .day-hours-form {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .hours-inputs {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .time-inputs {
    width: 100%;
  }

  .checkbox-group {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .specialties-tags {
    flex-direction: column;
  }

  .specialty-tag {
    text-align: center;
  }

  .day-hours {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
/* Booki
ng System Styles */

/* Booking Page Layout */
.booking-section {
  padding: 2rem 0;
  background-color: #f8f9fa;
}

.booking-container {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Service Information Card */
.service-info-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 2rem;
}

.service-info-card h2 {
  color: #333333;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #6d2e82;
}

.service-details h3 {
  color: #6d2e82;
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.shop-info {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.shop-info h4 {
  color: #333333;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.branch-name {
  color: #6d2e82;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.address {
  color: #666666;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.phone {
  color: #666666;
  font-size: 0.95rem;
}

.service-description {
  margin-bottom: 1.5rem;
}

.service-description p {
  color: #555555;
  line-height: 1.6;
}

.service-meta {
  display: flex;
  gap: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.service-meta .duration,
.service-meta .price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333333;
  font-weight: 500;
}

.service-meta .icon {
  font-size: 18px;
}

/* Booking Form Card */
.booking-form-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.booking-form-card h2 {
  color: #333333;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #6d2e82;
}

.booking-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Date Input Styles */
input[type="date"] {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  background-color: #ffffff;
}

input[type="date"]:focus {
  outline: none;
  border-color: #6d2e82;
  box-shadow: 0 0 0 3px rgba(109, 46, 130, 0.1);
}

/* Time Slots Container */
#time-slots-container {
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-slots-placeholder {
  color: #999999;
  font-style: italic;
  text-align: center;
}

.loading {
  color: #6d2e82;
  text-align: center;
  font-style: italic;
}

.no-slots {
  color: #e53e3e;
  text-align: center;
  font-style: italic;
}

.error {
  color: #e53e3e;
  text-align: center;
}

/* Time Slots Grid */
.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.time-slot {
  position: relative;
  cursor: pointer;
}

.time-slot input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.time-slot-label {
  display: block;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333333;
  background-color: #ffffff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.time-slot:hover .time-slot-label {
  border-color: #6d2e82;
  background-color: rgba(109, 46, 130, 0.05);
}

.time-slot input[type="radio"]:checked + .time-slot-label {
  border-color: #6d2e82;
  background-color: #6d2e82;
  color: #ffffff;
}

.time-slot input[type="radio"]:focus + .time-slot-label {
  outline: 2px solid #6d2e82;
  outline-offset: 2px;
}

/* Select Styles */
select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  background-color: #ffffff;
  cursor: pointer;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

select:focus {
  outline: none;
  border-color: #6d2e82;
  box-shadow: 0 0 0 3px rgba(109, 46, 130, 0.1);
}

/* Textarea Styles */
textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

textarea:focus {
  outline: none;
  border-color: #6d2e82;
  box-shadow: 0 0 0 3px rgba(109, 46, 130, 0.1);
}

/* Character Count */
.char-count {
  text-align: right;
  font-size: 0.85rem;
  color: #777777;
  margin-top: 0.25rem;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
}

.btn:disabled {
  background-color: #e1e5e9;
  color: #999999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn:disabled:hover {
  background-color: #e1e5e9;
  color: #999999;
  transform: none;
  box-shadow: none;
}

/* Working Hours Information */
.working-hours-info {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.working-hours-info h3 {
  color: #333333;
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #6d2e82;
}

.hours-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.hours-item .day {
  font-weight: 500;
  color: #333333;
}

.hours-item .hours {
  color: #666666;
  font-size: 0.9rem;
}

/* Alert Styles for Booking */
.alert {
  padding: 1rem 1.25rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  border: 1px solid transparent;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border-color: #bee5eb;
}

/* Responsive Design for Booking */
@media (max-width: 1024px) {
  .booking-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-info-card {
    position: static;
  }
}

@media (max-width: 768px) {
  .booking-section {
    padding: 1rem 0;
  }

  .service-info-card,
  .booking-form-card,
  .working-hours-info {
    padding: 1.5rem;
  }

  .service-meta {
    flex-direction: column;
    gap: 0.75rem;
  }

  .time-slots-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .time-slot-label {
    padding: 0.6rem 0.75rem;
    font-size: 0.85rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .btn {
    width: 100%;
  }

  .hours-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .service-info-card,
  .booking-form-card,
  .working-hours-info {
    padding: 1rem;
  }

  .service-info-card h2,
  .booking-form-card h2,
  .working-hours-info h3 {
    font-size: 1.25rem;
  }

  .time-slots-grid {
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
  }

  .hours-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    padding: 0.75rem;
  }
}

/* Loading and Error States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6d2e82;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Accessibility Improvements */
.time-slot input[type="radio"]:focus + .time-slot-label {
  outline: 2px solid #6d2e82;
  outline-offset: 2px;
}

.btn:focus {
  outline: 2px solid #6d2e82;
  outline-offset: 2px;
}

/* Print Styles for Booking */
@media print {
  .booking-section {
    background-color: white;
  }

  .service-info-card,
  .booking-form-card,
  .working-hours-info {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .form-actions {
    display: none;
  }
}
/* Cu
stomer Dashboard Styles */
.dashboard-header {
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.dashboard-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.welcome-section h1 {
  color: #6d2e82;
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.welcome-section p {
  color: #555555;
  font-size: 1.1rem;
}

.quick-actions .btn {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Dashboard Statistics */
.dashboard-stats {
  margin-bottom: 3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: #ffffff;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  border-radius: 50%;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #6d2e82;
  line-height: 1;
}

.stat-label {
  color: #777777;
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

/* Appointments Section */
.appointments-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.section-header h2 {
  color: #333333;
  font-size: 1.75rem;
  margin: 0;
}

/* Appointments Grid */
.appointments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.appointment-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.appointment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.appointment-card.upcoming {
  border-left: 4px solid #38a169;
}

.appointment-card.past {
  border-left: 4px solid #777777;
}

/* Appointment Header */
.appointment-header {
  background: #f8f9fa;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e1e5e9;
}

.appointment-date {
  text-align: center;
  min-width: 60px;
}

.date-day {
  font-size: 1.5rem;
  font-weight: 700;
  color: #6d2e82;
  line-height: 1;
}

.date-month {
  font-size: 0.8rem;
  color: #777777;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.appointment-time {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333333;
}

.appointment-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-confirmed {
  background: #d4edda;
  color: #155724;
}

.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

/* Appointment Details */
.appointment-details {
  padding: 1.5rem;
}

.service-name {
  color: #6d2e82;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.shop-info h4 {
  color: #333333;
  font-size: 1.1rem;
  margin: 0 0 0.25rem 0;
}

.branch-name {
  color: #666666;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.address {
  color: #777777;
  font-size: 0.85rem;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.phone {
  color: #6d2e82;
  font-size: 0.85rem;
  margin: 0;
}

.staff-info {
  margin: 1rem 0;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.staff-label {
  color: #777777;
  font-size: 0.85rem;
  font-weight: 500;
}

.staff-name {
  color: #333333;
  font-weight: 600;
}

.appointment-meta {
  display: flex;
  gap: 1.5rem;
  margin: 1rem 0;
  flex-wrap: wrap;
}

.appointment-meta > div {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666666;
  font-size: 0.9rem;
}

.appointment-meta .icon {
  font-size: 1rem;
}

.appointment-notes {
  margin: 1rem 0;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #555555;
}

.cancellation-reason {
  margin: 1rem 0;
  padding: 0.75rem;
  background: #fff3cd;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #856404;
}

/* Review Info */
.review-info {
  margin: 1rem 0;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.rating {
  margin-bottom: 0.5rem;
}

.star {
  color: #ddd;
  font-size: 1.2rem;
}

.star.filled {
  color: #ffd700;
}

.review-comment {
  font-size: 0.9rem;
  color: #555555;
  margin: 0;
  font-style: italic;
}

/* Appointment Actions */
.appointment-actions {
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-top: 1px solid #e1e5e9;
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.cancel-disabled {
  color: #999999;
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
  cursor: help;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #777777;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  color: #333333;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.empty-state p {
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content {
  background: #ffffff;
  border-radius: 12px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  color: #333333;
  font-size: 1.25rem;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #999999;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.modal-close:hover {
  background-color: #f8f9fa;
  color: #333333;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid #e1e5e9;
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.cancellation-policy {
  margin: 1rem 0;
  padding: 1rem;
  background: #fff3cd;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #856404;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header-content {
    flex-direction: column;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-bottom: 0.5rem;
  }

  .appointments-grid {
    grid-template-columns: 1fr;
  }

  .appointment-header {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .appointment-meta {
    flex-direction: column;
    gap: 0.75rem;
  }

  .appointment-actions {
    flex-direction: column;
  }

  .section-header {
    flex-direction: column;
    text-align: center;
  }

  .modal-content {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .modal-footer {
    flex-direction: column-reverse;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 1.5rem 0;
  }

  .welcome-section h1 {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .appointment-card {
    margin: 0 -0.5rem;
  }

  .appointment-details {
    padding: 1rem;
  }

  .appointment-actions {
    padding: 1rem;
  }
}

/* Appointment Management Styles */
.appointments-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.view-controls {
  display: flex;
  gap: 10px;
}

.view-btn {
  padding: 8px 16px;
  border: 1px solid #e9ecef;
  background: #ffffff;
  color: #333333;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn:hover {
  border-color: #6d2e82;
  color: #6d2e82;
}

.view-btn.active {
  background-color: #6d2e82;
  color: #ffffff;
  border-color: #6d2e82;
}

.date-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-controls input[type="date"] {
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
}

.appointments-container {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
  color: #777777;
  font-size: 16px;
}

.day-view,
.week-view,
.month-view {
  padding: 30px;
}

.day-view h3,
.week-view h3,
.month-view h3 {
  color: #6d2e82;
  margin-bottom: 25px;
  font-size: 24px;
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.appointment-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
}

.appointment-card:hover {
  border-color: #6d2e82;
  box-shadow: 0 2px 10px rgba(109, 46, 130, 0.1);
}

.appointment-time {
  min-width: 120px;
  text-align: center;
}

.appointment-time strong {
  display: block;
  font-size: 18px;
  color: #333333;
  margin-bottom: 5px;
}

.duration {
  font-size: 12px;
  color: #777777;
}

.appointment-info {
  flex: 1;
}

.appointment-info h4 {
  color: #6d2e82;
  margin-bottom: 8px;
  font-size: 16px;
}

.appointment-info p {
  margin-bottom: 4px;
  font-size: 14px;
  color: #777777;
}

.appointment-status {
  min-width: 100px;
  text-align: center;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.status-confirmed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.status-completed {
  background-color: #cce5ff;
  color: #004085;
}

.status-badge.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.appointment-card.status-pending {
  border-left: 4px solid #ffc107;
}

.appointment-card.status-confirmed {
  border-left: 4px solid #28a745;
}

.appointment-card.status-completed {
  border-left: 4px solid #007bff;
}

.appointment-card.status-cancelled {
  border-left: 4px solid #dc3545;
  opacity: 0.7;
}

.day-group {
  margin-bottom: 30px;
}

.day-group h4 {
  color: #333333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f8f9fa;
  font-size: 18px;
}

/* Appointment Modal Styles */
.appointment-details {
  margin-bottom: 25px;
}

.appointment-info-modal {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.appointment-info-modal h4 {
  color: #6d2e82;
  margin-bottom: 15px;
  font-size: 20px;
}

.appointment-info-modal p {
  margin-bottom: 8px;
  font-size: 14px;
}

.appointment-info-modal strong {
  color: #333333;
  font-weight: 600;
  min-width: 100px;
  display: inline-block;
}

.appointment-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.action-form {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.action-form h4 {
  color: #333333;
  margin-bottom: 15px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
}

/* Staff Management Styles */
.staff-grid {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.branch-staff {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.staff-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.staff-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
}

.staff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.staff-header h4 {
  color: #333333;
  margin: 0;
}

.staff-actions {
  display: flex;
  gap: 8px;
}

.staff-details p {
  margin-bottom: 10px;
  font-size: 14px;
}

.staff-specialties {
  margin-bottom: 15px;
}

.specialties-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.specialty-tag {
  background: #6d2e82;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.working-hours {
  margin-top: 15px;
}

.hours-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
  margin-top: 10px;
}

.day-hours {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #f1f3f4;
  font-size: 13px;
}

.day-hours:last-child {
  border-bottom: none;
}

.day {
  font-weight: 500;
  color: #333333;
}

.hours {
  color: #777777;
}

/* Responsive Design for Appointments */
@media (max-width: 768px) {
  .appointments-controls {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .view-controls {
    justify-content: center;
  }

  .date-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .appointment-card {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .appointment-time {
    min-width: auto;
    text-align: left;
  }

  .appointment-status {
    min-width: auto;
    text-align: left;
  }

  .appointment-actions {
    flex-direction: column;
    gap: 10px;
  }

  .form-actions {
    flex-direction: column;
  }

  .staff-list {
    grid-template-columns: 1fr;
  }

  .hours-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .day-view,
  .week-view,
  .month-view {
    padding: 20px;
  }

  .appointment-card {
    padding: 15px;
  }

  .appointment-info-modal {
    padding: 15px;
  }

  .action-form {
    padding: 15px;
  }
}
/* User
 Management Styles */
.user-stats {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-item {
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item h4 {
    font-size: 24px;
    color: #6d2e82;
    margin-bottom: 5px;
}

.stat-item p {
    color: #777777;
    font-size: 14px;
}

.user-filters {
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-form {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.users-table {
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.users-table table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.users-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.users-table tr:hover {
    background-color: #f8f9fa;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.role-badge.role-customer {
    background-color: #e3f2fd;
    color: #1976d2;
}

.role-badge.role-shop_owner {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.role-badge.role-admin {
    background-color: #ffebee;
    color: #d32f2f;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.active {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-badge.suspended {
    background-color: #fff3e0;
    color: #f57c00;
}

.status-badge.pending {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.badge {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.badge.badge-info {
    background-color: #e3f2fd;
    color: #1976d2;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.recent-activity {
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recent-activity h3 {
    margin-bottom: 15px;
    color: #333;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-info {
    flex: 1;
}

.activity-type {
    color: #777;
    font-size: 14px;
    margin-left: 5px;
}

.activity-time {
    color: #999;
    font-size: 12px;
}

/* Modal Styles for User Management */
.user-details-full {
    max-height: 400px;
    overflow-y: auto;
}

.user-basic-info,
.user-activity-info,
.user-business-info {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.user-basic-info:last-child,
.user-activity-info:last-child,
.user-business-info:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.user-details-full h4 {
    color: #6d2e82;
    margin-bottom: 10px;
    font-size: 16px;
}

.detail-row {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.detail-row strong {
    min-width: 120px;
    color: #333;
}

.detail-row ul {
    margin: 0;
    padding-left: 20px;
}

.detail-row li {
    margin-bottom: 4px;
}

.warning-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    text-align: center;
}

.error-message {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #777;
}

/* Responsive Design for User Management */
@media (max-width: 768px) {
    .filter-form {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: 100%;
    }
    
    .users-table {
        overflow-x: auto;
    }
    
    .users-table table {
        min-width: 600px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
/* ===== 
COMPREHENSIVE RESPONSIVE CSS STYLING SYSTEM ===== */

/* Dashboard Styles */
.dashboard-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.dashboard-header {
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: #333333;
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  color: #666666;
  font-size: 1.1rem;
}

.dashboard-nav {
  background-color: #ffffff;
  padding: 1rem 0;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-tabs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.nav-tab {
  padding: 0.75rem 1.5rem;
  background-color: #f8f9fa;
  color: #666666;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid #e1e5e9;
}

.nav-tab:hover {
  background-color: #e9ecef;
  color: #333333;
}

.nav-tab.active {
  background-color: #6d2e82;
  color: #ffffff;
  border-color: #6d2e82;
}

.dashboard-content {
  display: grid;
  gap: 2rem;
}

.dashboard-section {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333333;
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 0.5rem;
}

/* Stats Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  border-left: 4px solid #6d2e82;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #6d2e82;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #666666;
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-change {
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.stat-change.positive {
  color: #38a169;
}

.stat-change.negative {
  color: #e53e3e;
}

/* Data Tables */
.data-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.data-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333333;
  font-size: 0.9rem;
}

.data-table td {
  color: #555555;
  font-size: 0.9rem;
}

.data-table tr:hover {
  background-color: #f8f9fa;
}

.table-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  border-radius: 4px;
}

/* Status Badges */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-confirmed {
  background-color: #d4edda;
  color: #155724;
}

.status-completed {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.status-approved {
  background-color: #d4edda;
  color: #155724;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-suspended {
  background-color: #e2e3e5;
  color: #383d41;
}

.status-active {
  background-color: #d4edda;
  color: #155724;
}

/* Forms in Dashboard */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-section {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.form-section h4 {
  color: #333333;
  margin-bottom: 1rem;
  font-size: 1rem;
}

/* Calendar Styles */
.calendar {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e1e5e9;
}

.calendar-header {
  background-color: #6d2e82;
  color: #ffffff;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendar-nav {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.calendar-nav:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.calendar-title {
  font-size: 1.1rem;
  font-weight: 600;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar-day-header {
  padding: 0.75rem;
  background-color: #f8f9fa;
  text-align: center;
  font-weight: 500;
  color: #666666;
  font-size: 0.9rem;
  border-bottom: 1px solid #e1e5e9;
}

.calendar-day {
  padding: 0.75rem;
  min-height: 80px;
  border-bottom: 1px solid #e1e5e9;
  border-right: 1px solid #e1e5e9;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.calendar-day:hover {
  background-color: #f8f9fa;
}

.calendar-day.other-month {
  color: #cccccc;
  background-color: #f8f9fa;
}

.calendar-day.today {
  background-color: rgba(109, 46, 130, 0.1);
  font-weight: 600;
}

.calendar-day.selected {
  background-color: #6d2e82;
  color: #ffffff;
}

.day-number {
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.day-appointments {
  font-size: 0.7rem;
  color: #666666;
}

.appointment-dot {
  width: 6px;
  height: 6px;
  background-color: #6d2e82;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.25rem;
}

/* Booking Form Styles */
.booking-form {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.booking-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
}

.booking-steps::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e1e5e9;
  z-index: 1;
}

.booking-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 8px;
  min-width: 120px;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e1e5e9;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.booking-step.active .step-number {
  background-color: #6d2e82;
  color: #ffffff;
}

.booking-step.completed .step-number {
  background-color: #38a169;
  color: #ffffff;
}

.step-label {
  font-size: 0.9rem;
  color: #666666;
  text-align: center;
}

.booking-step.active .step-label {
  color: #6d2e82;
  font-weight: 500;
}

/* Time Slots */
.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
}

.time-slot {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #ffffff;
  font-size: 0.9rem;
}

.time-slot:hover {
  border-color: #6d2e82;
  background-color: #f8f9fa;
}

.time-slot.selected {
  border-color: #6d2e82;
  background-color: #6d2e82;
  color: #ffffff;
}

.time-slot.unavailable {
  background-color: #f8f9fa;
  color: #999999;
  cursor: not-allowed;
  border-color: #e1e5e9;
}

.time-slot.unavailable:hover {
  border-color: #e1e5e9;
  background-color: #f8f9fa;
}

/* Booking Summary */
.booking-summary {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  margin-top: 2rem;
}

.booking-summary h4 {
  color: #333333;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e1e5e9;
}

.summary-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
  font-weight: 600;
  font-size: 1.1rem;
}

.summary-label {
  color: #666666;
}

.summary-value {
  color: #333333;
  font-weight: 500;
}

/* Error Pages */
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
  padding: 2rem;
}

.error-container {
  text-align: center;
  max-width: 600px;
}

.error-code {
  font-size: 6rem;
  font-weight: 700;
  color: #6d2e82;
  margin-bottom: 1rem;
  line-height: 1;
}

.error-title {
  font-size: 2rem;
  color: #333333;
  margin-bottom: 1rem;
}

.error-message {
  color: #666666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Shop Profile Responsive Design */
@media (max-width: 768px) {
  .shop-header-content {
    flex-direction: column;
    gap: 1.5rem;
  }

  .shop-title {
    font-size: 2rem;
  }

  .shop-rating {
    min-width: auto;
    width: 100%;
  }

  .tab-nav {
    flex-wrap: wrap;
  }

  .tab-btn {
    flex: 1 1 50%;
    min-width: 120px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .staff-grid {
    grid-template-columns: 1fr;
  }

  .service-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .service-price {
    text-align: left;
    min-width: auto;
  }

  .service-action {
    min-width: auto;
    width: 100%;
  }

  .rating-breakdown {
    flex-direction: column;
    gap: 1.5rem;
  }

  .overall-rating {
    min-width: auto;
  }

  .rating-bars {
    max-width: none;
  }

  .review-header {
    flex-direction: column;
    gap: 0.5rem;
  }

  .booking-service-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .service-book {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .shop-header {
    padding: 1.5rem 0;
  }

  .shop-title {
    font-size: 1.75rem;
  }

  .tabs {
    margin: 0 -1rem;
    border-radius: 0;
  }

  .tab-content {
    padding: 1.5rem 1rem;
  }

  .service-item,
  .booking-service-item {
    padding: 1rem;
  }

  .hours-row {
    padding: 0.75rem 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .day-hours {
    width: 100%;
    justify-content: space-between;
  }
}

/* Mobile Responsive Dashboard */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 1.5rem 0;
  }

  .dashboard-title {
    font-size: 1.75rem;
  }

  .nav-tabs {
    justify-content: center;
  }

  .nav-tab {
    flex: 1;
    text-align: center;
    min-width: 100px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }

  .dashboard-section {
    padding: 1.5rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .data-table {
    font-size: 0.8rem;
  }

  .data-table th,
  .data-table td {
    padding: 0.75rem 0.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .calendar-day {
    min-height: 60px;
    padding: 0.5rem;
  }

  .booking-steps {
    flex-direction: column;
    gap: 1rem;
  }

  .booking-steps::before {
    display: none;
  }

  .booking-step {
    flex-direction: row;
    justify-content: flex-start;
    min-width: auto;
    width: 100%;
  }

  .step-number {
    margin-bottom: 0;
    margin-right: 1rem;
  }

  .time-slots {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .dashboard-content {
    gap: 1rem;
  }

  .dashboard-section {
    padding: 1rem;
    margin: 0 -0.5rem;
    border-radius: 8px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1rem;
  }

  .data-table-wrapper {
    overflow-x: auto;
  }

  .booking-form {
    padding: 1.5rem 1rem;
    margin: 0 -0.5rem;
    border-radius: 8px;
  }

  .booking-summary {
    margin: 1rem -0.5rem 0;
    border-radius: 8px;
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: 6px; }
.rounded-lg { border-radius: 12px; }
.rounded-full { border-radius: 50%; }

.shadow { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }
.shadow-xl { box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); }

/* Print Styles */
@media print {
  .header,
  .footer,
  .nav,
  .btn,
  .search-form {
    display: none !important;
  }

  .container {
    max-width: none;
    padding: 0;
  }

  .dashboard-section,
  .shop-card,
  .booking-form {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .data-table {
    font-size: 0.8rem;
  }

  .data-table th,
  .data-table td {
    padding: 0.5rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .btn-primary {
    background-color: #000000;
    border: 2px solid #000000;
  }

  .btn-primary:hover {
    background-color: #333333;
  }

  .form-control:focus {
    border-color: #000000;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.3);
  }

  .nav-tab.active {
    background-color: #000000;
    border-color: #000000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-color: #404040;
  }

  body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }

  .auth-card,
  .dashboard-section,
  .shop-card,
  .booking-form {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
  }

  .form-control {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
  }

  .data-table th {
    background-color: var(--bg-primary);
  }

  .data-table tr:hover {
    background-color: var(--bg-primary);
  }
}

/* --- SHOP OWNER DASHBOARD --- */
.shop-owner-dashboard .dashboard-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e1e5f2 100%);
}
.shop-owner-dashboard .dashboard-nav {
  background: #fff7e6;
  border-right: 2px solid #f7c873;
}
.shop-owner-dashboard .nav-header h2 {
  color: #e67e22;
}
.shop-owner-dashboard .nav-link.active {
  background: #e67e22;
  color: #fff;
}
.shop-owner-dashboard .dashboard-main {
  background: #fff;
}
.shop-owner-dashboard .dashboard-section {
  border-left: 6px solid #e67e22;
  background: #fffdfa;
}
.shop-owner-dashboard .stat-item {
  background: #fff7e6;
  border: 1px solid #f7c873;
}
.shop-owner-dashboard .stat-item h4 {
  color: #e67e22;
}
.shop-owner-dashboard .action-card {
  border-color: #f7c873;
}
.shop-owner-dashboard .action-card:hover {
  border-color: #e67e22;
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.15);
}
.shop-owner-dashboard .shop-status-header h3 {
  color: #e67e22;
}
.shop-owner-dashboard .status-badge.approved {
  background: #ffe5b4;
  color: #e67e22;
}
.shop-owner-dashboard .status-badge.pending {
  background: #fff3cd;
  color: #b8860b;
}
.shop-owner-dashboard .status-badge.rejected,
.shop-owner-dashboard .status-badge.suspended {
  background: #f8d7da;
  color: #c0392b;
}

/* --- CUSTOMER DASHBOARD --- */
.customer-dashboard .dashboard-header {
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
}
.customer-dashboard .dashboard-nav {
  background: #f8c1c1;
  border-right: 2px solid #c1c8e4;
}
.customer-dashboard .nav-header h2 {
  color: #6d2e82;
}
.customer-dashboard .nav-link.active {
  background: #6d2e82;
  color: #fff;
}
.customer-dashboard .dashboard-main {
  background: #f9f7fc;
}
.customer-dashboard .dashboard-section {
  border-left: 6px solid #6d2e82;
  background: #fff;
}
.customer-dashboard .stat-card {
  background: #f8c1c1;
  border: 1px solid #c1c8e4;
}
.customer-dashboard .stat-icon {
  background: linear-gradient(135deg, #f8c1c1 0%, #c1c8e4 100%);
}
.customer-dashboard .stat-number {
  color: #6d2e82;
}
.customer-dashboard .action-card {
  border-color: #c1c8e4;
}
.customer-dashboard .action-card:hover {
  border-color: #6d2e82;
  box-shadow: 0 4px 15px rgba(109, 46, 130, 0.15);
}

/* --- GENERAL DASHBOARD IMPROVEMENTS --- */
.dashboard-section {
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.stat-card, .stat-item {
  transition: box-shadow 0.2s, transform 0.2s;
}
.stat-card:hover, .stat-item:hover {
  box-shadow: 0 4px 20px rgba(0,0,0,0.10);
  transform: translateY(-2px);
}

/* --- Modern Page Section & Main Layout --- */
.page-section {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2.5rem 1.5rem;
  background: transparent;
  display: block;
}

@media (max-width: 900px) {
  .page-section {
    max-width: 98vw;
    padding: 2rem 0.5rem;
  }
}
@media (max-width: 600px) {
  .page-section {
    padding: 1.2rem 0.2rem;
  }
}

.page-main {
  min-height: 70vh;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 6px 32px rgba(109,46,130,0.07);
  margin: 2.5rem auto 2rem auto;
  padding-bottom: 2rem;
  position: relative;
  z-index: 2;
}

/* Remove old .container and .main styles */
.container, .main {
  all: unset;
}