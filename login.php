<?php
/**
 * User Login Page
 * Handles user authentication with form validation and security features
 */
 
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $redirectUrl = $_SESSION['redirect_after_login'] ?? '/dashboard/' . $_SESSION['user_role'] . '.php';
    unset($_SESSION['redirect_after_login']);
    redirect($redirectUrl);
}

$errors = [];
$email = '';
$loginSuccess = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $errors['general'] = 'Security token mismatch. Please try again.';
    } else {
        $email = sanitizeEmail($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        
        // Basic validation
        if (empty($email)) {
            $errors['email'] = 'Email is required';
        } elseif (!validateEmail($email)) {
            $errors['email'] = 'Please enter a valid email address';
        }
        
        if (empty($password)) {
            $errors['password'] = 'Password is required';
        }
        
        // Check rate limiting
        if (empty($errors) && !$auth->checkLoginRateLimit($email)) {
            $errors['general'] = 'Too many failed login attempts. Please try again in 15 minutes.';
        }
        
        // Attempt authentication if no validation errors
        if (empty($errors)) {
            $result = $auth->authenticateUser($email, $password);
            
            if ($result['success']) {
                // Clear any failed login attempts
                $auth->clearLoginAttempts($email);
                
                // Update last login time
                $auth->updateLastLogin($result['user']['id']);
                
                // Set success message
                setFlashMessage('success', 'Welcome back, ' . $result['user']['first_name'] . '!');
                
                // Redirect to appropriate dashboard or requested page
                $redirectUrl = $_SESSION['redirect_after_login'] ?? '/dashboard/' . $result['user']['role'] . '.php';
                unset($_SESSION['redirect_after_login']);
                redirect($redirectUrl);
            } else {
                // Record failed login attempt
                $auth->recordFailedLogin($email);
                
                // Show error message
                $errors['general'] = $result['message'];
            }
        }
    }
}

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Welcome Back</h1>
                <p>Sign in to your account</p>
            </div>
            
            <!-- Flash Messages -->
            <?php if (!empty($flashMessages)): ?>
                <div class="flash-messages">
                    <?php foreach ($flashMessages as $message): ?>
                        <div class="alert alert-<?php echo htmlspecialchars($message['type']); ?>">
                            <?php echo htmlspecialchars($message['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <!-- General Error Message -->
            <?php if (isset($errors['general'])): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($errors['general']); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="login.php" class="auth-form" novalidate>
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(csrfToken()); ?>">
                
                <!-- Email Field -->
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        value="<?php echo htmlspecialchars($email); ?>"
                        class="form-control <?php echo isset($errors['email']) ? 'error' : ''; ?>"
                        placeholder="Enter your email address"
                        required
                        autocomplete="email"
                    >
                    <?php if (isset($errors['email'])): ?>
                        <div class="error-message"><?php echo htmlspecialchars($errors['email']); ?></div>
                    <?php endif; ?>
                </div>
                
                <!-- Password Field -->
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="password-input-wrapper">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control <?php echo isset($errors['password']) ? 'error' : ''; ?>"
                            placeholder="Enter your password"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <span class="show-text">Show</span>
                            <span class="hide-text" style="display: none;">Hide</span>
                        </button>
                    </div>
                    <?php if (isset($errors['password'])): ?>
                        <div class="error-message"><?php echo htmlspecialchars($errors['password']); ?></div>
                    <?php endif; ?>
                </div>
                
                <!-- Remember Me and Forgot Password -->
                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember_me" value="1">
                        <span class="checkmark"></span>
                        Remember me
                    </label>
                    <a href="forgot-password.php" class="forgot-password-link">Forgot Password?</a>
                </div>
                
                <!-- Submit Button -->
                <button type="submit" class="btn btn-primary btn-full">
                    Sign In
                </button>
            </form>
            
            <!-- Registration Link -->
            <div class="auth-footer">
                <p>Don't have an account? <a href="register.php">Sign up here</a></p>
            </div>
        </div>
    </div>
    
    <!-- JavaScript for password toggle -->
    <script>
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleButton = passwordField.nextElementSibling;
            const showText = toggleButton.querySelector('.show-text');
            const hideText = toggleButton.querySelector('.hide-text');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                showText.style.display = 'none';
                hideText.style.display = 'inline';
            } else {
                passwordField.type = 'password';
                showText.style.display = 'inline';
                hideText.style.display = 'none';
            }
        }
        
        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.auth-form');
            const emailField = document.getElementById('email');
            const passwordField = document.getElementById('password');
            
            // Real-time email validation
            emailField.addEventListener('blur', function() {
                const email = this.value.trim();
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                
                if (email && !emailRegex.test(email)) {
                    this.classList.add('error');
                    showFieldError(this, 'Please enter a valid email address');
                } else {
                    this.classList.remove('error');
                    hideFieldError(this);
                }
            });
            
            // Form submission validation
            form.addEventListener('submit', function(e) {
                let hasErrors = false;
                
                // Validate email
                const email = emailField.value.trim();
                if (!email) {
                    showFieldError(emailField, 'Email is required');
                    hasErrors = true;
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                    showFieldError(emailField, 'Please enter a valid email address');
                    hasErrors = true;
                } else {
                    hideFieldError(emailField);
                }
                
                // Validate password
                const password = passwordField.value;
                if (!password) {
                    showFieldError(passwordField, 'Password is required');
                    hasErrors = true;
                } else {
                    hideFieldError(passwordField);
                }
                
                if (hasErrors) {
                    e.preventDefault();
                }
            });
            
            function showFieldError(field, message) {
                field.classList.add('error');
                
                // Remove existing error message
                const existingError = field.parentNode.querySelector('.error-message');
                if (existingError) {
                    existingError.remove();
                }
                
                // Add new error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }
            
            function hideFieldError(field) {
                field.classList.remove('error');
                const errorMessage = field.parentNode.querySelector('.error-message');
                if (errorMessage) {
                    errorMessage.remove();
                }
            }
        });
    </script>
</body>
</html>