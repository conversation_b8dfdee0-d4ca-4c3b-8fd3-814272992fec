<?php
/**
 * Database Management Web Interface
 * Provides a simple web interface for database operations
 */

require_once 'includes/config.php';
require_once 'includes/install.php';
require_once 'includes/backup.php';
require_once 'includes/migrate.php';

// Simple authentication check (in production, use proper authentication)
session_start();
$isAuthenticated = isset($_SESSION['db_admin']) && $_SESSION['db_admin'] === true;

if (isset($_POST['admin_password']) && $_POST['admin_password'] === 'admin123') {
    $_SESSION['db_admin'] = true;
    $isAuthenticated = true;
}

if (isset($_POST['logout'])) {
    unset($_SESSION['db_admin']);
    $isAuthenticated = false;
}

if (!$isAuthenticated) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Database Manager - Authentication</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 50px auto; max-width: 400px; }
            .form-group { margin: 15px 0; }
            input[type="password"], input[type="submit"] { width: 100%; padding: 10px; }
            .error { color: red; margin: 10px 0; }
        </style>
    </head>
    <body>
        <h2>Database Manager Authentication</h2>
        <?php if (isset($_POST['admin_password'])): ?>
            <div class="error">Invalid password</div>
        <?php endif; ?>
        <form method="post">
            <div class="form-group">
                <label>Admin Password:</label>
                <input type="password" name="admin_password" required>
            </div>
            <div class="form-group">
                <input type="submit" value="Login">
            </div>
        </form>
        <p><small>Default password: admin123</small></p>
    </body>
    </html>
    <?php
    exit;
}

// Handle actions
$action = $_GET['action'] ?? 'dashboard';
$message = '';
$error = '';

switch ($action) {
    case 'install':
        $installer = new DatabaseInstaller();
        if ($installer->isInstallationNeeded()) {
            ob_start();
            $installer->install();
            $message = ob_get_clean();
        } else {
            $error = "Database already installed";
        }
        break;
        
    case 'backup':
        $backup = new DatabaseBackup();
        $backupPath = $backup->createAutomatedBackup();
        if ($backupPath) {
            $message = "Backup created: " . basename($backupPath);
        } else {
            $error = implode(', ', $backup->getErrors());
        }
        break;
        
    case 'restore':
        if (isset($_POST['backup_file'])) {
            $backup = new DatabaseBackup();
            $backupFile = __DIR__ . '/backups/' . $_POST['backup_file'];
            if ($backup->restoreBackup($backupFile)) {
                $message = "Database restored successfully";
            } else {
                $error = implode(', ', $backup->getErrors());
            }
        }
        break;
        
    case 'migrate':
        $migration = new DatabaseMigration();
        $migration->migrate();
        $message = implode(', ', $migration->getSuccess());
        if (!empty($migration->getErrors())) {
            $error = implode(', ', $migration->getErrors());
        }
        break;
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Beauty Platform - Database Manager</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .header { 
            border-bottom: 2px solid #F8C1C1; 
            padding-bottom: 20px; 
            margin-bottom: 30px; 
        }
        .nav { 
            margin: 20px 0; 
        }
        .nav a { 
            display: inline-block; 
            padding: 10px 20px; 
            margin-right: 10px; 
            background: #F8C1C1; 
            color: #333; 
            text-decoration: none; 
            border-radius: 4px; 
        }
        .nav a:hover, .nav a.active { 
            background: #6D2E82; 
            color: white; 
        }
        .message { 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 4px; 
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb; 
        }
        .card { 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            padding: 20px; 
            margin: 20px 0; 
        }
        .card h3 { 
            margin-top: 0; 
            color: #6D2E82; 
        }
        .button { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #6D2E82; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            border: none; 
            cursor: pointer; 
            margin: 5px; 
        }
        .button:hover { 
            background: #5a2569; 
        }
        .button.secondary { 
            background: #C1C8E4; 
            color: #333; 
        }
        .button.danger { 
            background: #dc3545; 
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 20px 0; 
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 12px; 
            text-align: left; 
        }
        th { 
            background: #f8f9fa; 
        }
        .status-executed { 
            color: #28a745; 
            font-weight: bold; 
        }
        .status-pending { 
            color: #ffc107; 
            font-weight: bold; 
        }
        .logout { 
            float: right; 
            margin-top: -10px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Beauty Platform - Database Manager</h1>
            <form method="post" class="logout">
                <input type="hidden" name="logout" value="1">
                <input type="submit" value="Logout" class="button secondary">
            </form>
        </div>
        
        <div class="nav">
            <a href="?action=dashboard" class="<?php echo $action === 'dashboard' ? 'active' : ''; ?>">Dashboard</a>
            <a href="?action=install" class="<?php echo $action === 'install' ? 'active' : ''; ?>">Install</a>
            <a href="?action=backup" class="<?php echo $action === 'backup' ? 'active' : ''; ?>">Backup</a>
            <a href="?action=restore" class="<?php echo $action === 'restore' ? 'active' : ''; ?>">Restore</a>
            <a href="?action=migrate" class="<?php echo $action === 'migrate' ? 'active' : ''; ?>">Migrate</a>
        </div>
        
        <?php if ($message): ?>
            <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="message error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($action === 'dashboard'): ?>
            <div class="card">
                <h3>Database Status</h3>
                <?php
                try {
                    $installer = new DatabaseInstaller();
                    $needsInstall = $installer->isInstallationNeeded();
                    
                    if ($needsInstall) {
                        echo "<p>❌ Database not installed</p>";
                        echo "<a href='?action=install' class='button'>Install Database</a>";
                    } else {
                        echo "<p>✅ Database installed and ready</p>";
                        
                        // Show backup stats
                        $backup = new DatabaseBackup();
                        $stats = $backup->getBackupStats();
                        echo "<p>📁 Backups: {$stats['count']} files ({$stats['total_size_formatted']})</p>";
                        
                        // Show migration status
                        $migration = new DatabaseMigration();
                        $migrationStatus = $migration->getStatus();
                        $pending = array_filter($migrationStatus, function($m) { return !$m['executed']; });
                        
                        if (empty($pending)) {
                            echo "<p>✅ All migrations up to date</p>";
                        } else {
                            echo "<p>⚠️ " . count($pending) . " pending migrations</p>";
                            echo "<a href='?action=migrate' class='button'>Run Migrations</a>";
                        }
                    }
                } catch (Exception $e) {
                    echo "<p>❌ Error checking database status: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
                ?>
            </div>
            
            <div class="card">
                <h3>Quick Actions</h3>
                <a href="?action=backup" class="button">Create Backup</a>
                <a href="?action=migrate" class="button">Run Migrations</a>
                <a href="includes/install.php" class="button secondary" target="_blank">View Install Log</a>
            </div>
            
        <?php elseif ($action === 'backup'): ?>
            <div class="card">
                <h3>Database Backup</h3>
                <p>Create a complete backup of the database including all data and structure.</p>
                <a href="?action=backup" class="button">Create New Backup</a>
                
                <?php
                $backup = new DatabaseBackup();
                $backups = $backup->listBackups();
                
                if (!empty($backups)):
                ?>
                <h4>Available Backups</h4>
                <table>
                    <tr>
                        <th>Filename</th>
                        <th>Size</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                    <?php foreach ($backups as $b): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($b['filename']); ?></td>
                        <td><?php echo $backup->formatBytes($b['size']); ?></td>
                        <td><?php echo date('Y-m-d H:i:s', $b['created']); ?></td>
                        <td>
                            <form method="post" action="?action=restore" style="display: inline;">
                                <input type="hidden" name="backup_file" value="<?php echo htmlspecialchars($b['filename']); ?>">
                                <input type="submit" value="Restore" class="button danger" 
                                       onclick="return confirm('Are you sure? This will overwrite the current database!')">
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </table>
                <?php endif; ?>
            </div>
            
        <?php elseif ($action === 'migrate'): ?>
            <div class="card">
                <h3>Database Migrations</h3>
                <p>Manage database schema updates and versioning.</p>
                
                <?php
                $migration = new DatabaseMigration();
                $status = $migration->getStatus();
                ?>
                
                <table>
                    <tr>
                        <th>Version</th>
                        <th>Description</th>
                        <th>Status</th>
                    </tr>
                    <?php foreach ($status as $mig): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($mig['version']); ?></td>
                        <td><?php echo htmlspecialchars($mig['description']); ?></td>
                        <td class="<?php echo $mig['executed'] ? 'status-executed' : 'status-pending'; ?>">
                            <?php echo $mig['executed'] ? '✅ Executed' : '⏳ Pending'; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </table>
                
                <a href="?action=migrate" class="button">Run Pending Migrations</a>
            </div>
            
        <?php elseif ($action === 'install'): ?>
            <div class="card">
                <h3>Database Installation</h3>
                <p>Install the complete database schema with sample data.</p>
                
                <?php
                $installer = new DatabaseInstaller();
                if ($installer->isInstallationNeeded()) {
                    echo "<p>⚠️ Database installation is required.</p>";
                    echo "<a href='?action=install' class='button'>Install Database</a>";
                } else {
                    echo "<p>✅ Database is already installed.</p>";
                    echo "<p>If you need to reinstall, please drop the database first.</p>";
                }
                ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>