/* ===== COMPREHENSIVE RESPONSIVE CSS STYLING SYSTEM ===== */
/* Beauty Platform - Mobile-First Responsive Design */

/* Design System Colors and Typography */
:root {
  /* Beauty Industry Color Palette */
  --primary-color: #6d2e82; /* Deep Plum */
  --primary-light: #f8c1c1; /* Soft Rose Pink */
  --accent-color: #c1c8e4; /* Lavender Gray */
  --background-color: #ffffff; /* Ivory/White */
  --text-dark: #333333; /* Charcoal Gray */
  --text-light: #777777; /* Cool Gray */
  --success-color: #38a169; /* Emerald Green */
  --error-color: #e53e3e; /* Error Red */
  --warning-color: #856404; /* Warning Orange */
  --border-color: #e1e5e9; /* Light Border */
  --shadow-color: rgba(0, 0, 0, 0.1);
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px var(--shadow-color);
  --shadow-md: 0 4px 15px var(--shadow-color);
  --shadow-lg: 0 10px 30px var(--shadow-color);
}

/* Dashboard Styles */
.dashboard-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.dashboard-header {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--accent-color) 100%);
  padding: var(--spacing-xl) 0;
  margin-bottom: var(--spacing-xl);
}

.dashboard-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: var(--spacing-sm);
}

.dashboard-subtitle {
  color: var(--text-light);
  font-size: var(--font-size-lg);
}

.dashboard-nav {
  background-color: var(--background-color);
  padding: var(--spacing-md) 0;
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
}

.nav-tabs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.nav-tab {
  padding: 0.75rem 1.5rem;
  background-color: #f8f9fa;
  color: var(--text-light);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.nav-tab:hover {
  background-color: #e9ecef;
  color: var(--text-dark);
}

.nav-tab.active {
  background-color: var(--primary-color);
  color: var(--background-color);
  border-color: var(--primary-color);
}

.dashboard-content {
  display: grid;
  gap: var(--spacing-xl);
}

.dashboard-section {
  background-color: var(--background-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Stats Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--s