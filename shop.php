<?php

/**
 * Shop Profile Page - Beauty Platform
 * Displays detailed shop information, services, prices, working hours, and ratings
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Initialize database query helper
$db = new DatabaseQuery();

// Get shop ID and branch ID from URL parameters
$shopId = isset($_GET['id']) ? sanitizeInt($_GET['id']) : null;
$branchId = isset($_GET['branch']) ? sanitizeInt($_GET['branch']) : null;

// Validate shop ID
if (!$shopId || !validateId($shopId)) {
    header("HTTP/1.0 404 Not Found"); 
    include '404.php';
    exit();
}

try {
    // Get shop information
    $shop = $db->selectOne('shops', ['id' => $shopId, 'status' => 'approved']);

    if (!$shop) {
        header("HTTP/1.0 404 Not Found");
        include '404.php';
        exit();
    }

    // Get all branches for this shop
    $branches = $db->select(
        'shop_branches',
        ['shop_id' => $shopId, 'status' => 'active'],
        [],
        'branch_name ASC'
    );

    if (empty($branches)) {
        header("HTTP/1.0 404 Not Found");
        include '404.php';
        exit();
    }

    // Determine which branch to display
    $currentBranch = null;
    if ($branchId) {
        // Find the specific branch
        foreach ($branches as $branch) {
            if ($branch['id'] == $branchId) {
                $currentBranch = $branch;
                break;
            }
        }
    }

    // If no specific branch found or requested, use the first branch
    if (!$currentBranch) {
        $currentBranch = $branches[0];
        $branchId = $currentBranch['id'];
    }

    // Get services for the current branch
    $services = $db->select(
        'services',
        ['branch_id' => $branchId, 'status' => 'active'],
        [],
        'category ASC, name ASC'
    );

    // Group services by category
    $servicesByCategory = [];
    foreach ($services as $service) {
        $category = $service['category'] ?: 'General Services';
        $servicesByCategory[$category][] = $service;
    }

    // Get reviews for this shop
    $reviews = $db->query("
        SELECT 
            r.*,
            u.first_name,
            u.last_name
        FROM reviews r
        INNER JOIN users u ON r.customer_id = u.id
        WHERE r.shop_id = :shop_id AND r.status = 'active'
        ORDER BY r.created_at DESC
        LIMIT 20
    ", [':shop_id' => $shopId]);

    // Calculate average rating and review count
    $ratingStats = $db->queryOne("
        SELECT 
            COALESCE(AVG(rating), 0) as average_rating,
            COUNT(*) as review_count,
            SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
            SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
            SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
            SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
            SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
        FROM reviews 
        WHERE shop_id = :shop_id AND status = 'active'
    ", [':shop_id' => $shopId]);

    // Get staff for the current branch
    $staff = $db->select(
        'staff',
        ['branch_id' => $branchId, 'status' => 'active'],
        [],
        'name ASC'
    );

    // Parse working hours
    $workingHours = parseWorkingHours($currentBranch['working_hours']);
} catch (Exception $e) {
    logError("Failed to load shop profile: " . $e->getMessage(), [
        'shop_id' => $shopId,
        'branch_id' => $branchId
    ]);
    header("HTTP/1.0 500 Internal Server Error");
    include '500.php';
    exit();
}

$pageTitle = htmlspecialchars($shop['business_name']);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="header-content">
                <div class="logo">
                    <h1><a href="index.php">Beauty Platform</a></h1>
                </div>
                <nav class="nav">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="dashboard/<?php echo $_SESSION['role']; ?>.php" class="nav-link">Dashboard</a>
                        <a href="logout.php" class="nav-link">Logout</a>
                    <?php else: ?>
                        <a href="login.php" class="nav-link">Login</a>
                        <a href="register.php" class="nav-link">Register</a>
                    <?php endif; ?>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Breadcrumb -->
        <div class="container">
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php echo htmlspecialchars($shop['business_name']); ?></span>
            </nav>
        </div>

        <!-- Shop Header -->
        <section class="shop-header">
            <div class="container">
                <div class="shop-header-content">
                    <div class="shop-info">
                        <h1 class="shop-title"><?php echo htmlspecialchars($shop['business_name']); ?></h1>

                        <?php if (count($branches) > 1): ?>
                            <div class="branch-selector">
                                <label for="branch-select">Branch:</label>
                                <select id="branch-select" onchange="switchBranch(this.value)">
                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>"
                                            <?php echo $branch['id'] == $branchId ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($branch['branch_name']); ?> -
                                            <?php echo htmlspecialchars($branch['city']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        <?php endif; ?>

                        <div class="shop-location">
                            <span class="location-icon">📍</span>
                            <div class="location-details">
                                <div class="address"><?php echo htmlspecialchars($currentBranch['address']); ?></div>
                                <div class="city-country">
                                    <?php echo htmlspecialchars($currentBranch['city']); ?>,
                                    <?php echo htmlspecialchars($currentBranch['country']); ?>
                                </div>
                            </div>
                        </div>

                        <?php if (!empty($currentBranch['phone'])): ?>
                            <div class="shop-contact">
                                <span class="phone-icon">📞</span>
                                <a href="tel:<?php echo htmlspecialchars($currentBranch['phone']); ?>">
                                    <?php echo htmlspecialchars($currentBranch['phone']); ?>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($currentBranch['email'])): ?>
                            <div class="shop-contact">
                                <span class="email-icon">✉️</span>
                                <a href="mailto:<?php echo htmlspecialchars($currentBranch['email']); ?>">
                                    <?php echo htmlspecialchars($currentBranch['email']); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="shop-rating">
                        <?php if ($ratingStats['review_count'] > 0): ?>
                            <div class="rating-display">
                                <div class="rating-score">
                                    <?php echo number_format($ratingStats['average_rating'], 1); ?>
                                </div>
                                <div class="rating-stars">
                                    <?php
                                    $rating = round($ratingStats['average_rating']);
                                    for ($i = 1; $i <= 5; $i++):
                                        echo $i <= $rating ? '⭐' : '☆';
                                    endfor;
                                    ?>
                                </div>
                                <div class="rating-count">
                                    <?php echo $ratingStats['review_count']; ?>
                                    review<?php echo $ratingStats['review_count'] !== 1 ? 's' : ''; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="no-rating">
                                <div class="no-rating-text">No reviews yet</div>
                                <div class="be-first">Be the first to review!</div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- Shop Content Tabs -->
        <section class="shop-content">
            <div class="container">
                <div class="tabs">
                    <div class="tab-nav">
                        <button class="tab-btn active" onclick="showTab('overview')">Overview</button>
                        <button class="tab-btn" onclick="showTab('services')">Services & Prices</button>
                        <button class="tab-btn" onclick="showTab('hours')">Working Hours</button>
                        <button class="tab-btn" onclick="showTab('reviews')">Reviews</button>
                        <?php if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'customer'): ?>
                            <button class="tab-btn" onclick="showTab('booking')">Book Appointment</button>
                        <?php endif; ?>
                    </div>

                    <!-- Overview Tab -->
                    <div id="overview" class="tab-content active">
                        <div class="overview-content">
                            <?php if (!empty($shop['description'])): ?>
                                <div class="shop-description">
                                    <h3>About Us</h3>
                                    <p><?php echo nl2br(htmlspecialchars($shop['description'])); ?></p>
                                </div>
                            <?php endif; ?>

                            <div class="quick-info">
                                <div class="info-grid">
                                    <div class="info-item">
                                        <h4>Services Available</h4>
                                        <p><?php echo count($services); ?> service<?php echo count($services) !== 1 ? 's' : ''; ?></p>
                                    </div>

                                    <?php if (!empty($staff)): ?>
                                        <div class="info-item">
                                            <h4>Professional Staff</h4>
                                            <p><?php echo count($staff); ?> staff member<?php echo count($staff) !== 1 ? 's' : ''; ?></p>
                                        </div>
                                    <?php endif; ?>

                                    <div class="info-item">
                                        <h4>Location</h4>
                                        <p><?php echo htmlspecialchars($currentBranch['city']); ?>, <?php echo htmlspecialchars($currentBranch['country']); ?></p>
                                    </div>

                                    <?php if ($ratingStats['review_count'] > 0): ?>
                                        <div class="info-item">
                                            <h4>Customer Rating</h4>
                                            <p><?php echo number_format($ratingStats['average_rating'], 1); ?>/5 ⭐</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if (!empty($staff)): ?>
                                <div class="staff-section">
                                    <h3>Our Team</h3>
                                    <div class="staff-grid">
                                        <?php foreach ($staff as $member): ?>
                                            <div class="staff-card">
                                                <h4><?php echo htmlspecialchars($member['name']); ?></h4>
                                                <?php
                                                $specialties = json_decode($member['specialties'], true);
                                                if (!empty($specialties) && is_array($specialties)):
                                                ?>
                                                    <div class="specialties">
                                                        <strong>Specialties:</strong>
                                                        <?php echo htmlspecialchars(implode(', ', $specialties)); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Services Tab -->
                    <div id="services" class="tab-content">
                        <div class="services-content">
                            <?php if (!empty($servicesByCategory)): ?>
                                <?php foreach ($servicesByCategory as $category => $categoryServices): ?>
                                    <div class="service-category">
                                        <h3><?php echo htmlspecialchars($category); ?></h3>
                                        <div class="services-list">
                                            <?php foreach ($categoryServices as $service): ?>
                                                <div class="service-item">
                                                    <div class="service-info">
                                                        <h4><?php echo htmlspecialchars($service['name']); ?></h4>
                                                        <?php if (!empty($service['description'])): ?>
                                                            <p class="service-description">
                                                                <?php echo htmlspecialchars($service['description']); ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        <div class="service-duration">
                                                            <span class="duration-icon">⏱️</span>
                                                            <?php echo $service['duration_minutes']; ?> minutes
                                                        </div>
                                                    </div>
                                                    <div class="service-price">
                                                        <?php echo formatPrice($service['price']); ?>
                                                    </div>
                                                    <?php if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'customer'): ?>
                                                        <div class="service-action">
                                                            <button class="btn btn-primary btn-sm"
                                                                onclick="bookService(<?php echo $service['id']; ?>)">
                                                                Book Now
                                                            </button>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="no-services">
                                    <p>No services are currently available at this location.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Working Hours Tab -->
                    <div id="hours" class="tab-content">
                        <div class="hours-content">
                            <h3>Working Hours</h3>
                            <?php if (!empty($workingHours)): ?>
                                <div class="hours-table">
                                    <?php
                                    $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                                    $dayNames = [
                                        'monday' => 'Monday',
                                        'tuesday' => 'Tuesday',
                                        'wednesday' => 'Wednesday',
                                        'thursday' => 'Thursday',
                                        'friday' => 'Friday',
                                        'saturday' => 'Saturday',
                                        'sunday' => 'Sunday'
                                    ];

                                    foreach ($days as $day):
                                        $dayHours = $workingHours[$day] ?? null;
                                        $isToday = strtolower(date('l')) === $day;
                                    ?>
                                        <div class="hours-row <?php echo $isToday ? 'today' : ''; ?>">
                                            <div class="day-name">
                                                <?php echo $dayNames[$day]; ?>
                                                <?php if ($isToday): ?>
                                                    <span class="today-badge">Today</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="day-hours">
                                                <?php if ($dayHours && isset($dayHours['open']) && isset($dayHours['close'])): ?>
                                                    <?php echo formatTime($dayHours['open']); ?> - <?php echo formatTime($dayHours['close']); ?>
                                                    <?php if ($isToday): ?>
                                                        <?php
                                                        $currentTime = date('H:i');
                                                        $isOpen = $currentTime >= $dayHours['open'] && $currentTime <= $dayHours['close'];
                                                        ?>
                                                        <span class="status <?php echo $isOpen ? 'open' : 'closed'; ?>">
                                                            <?php echo $isOpen ? 'Open Now' : 'Closed'; ?>
                                                        </span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="closed">Closed</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <p>Working hours information is not available.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Reviews Tab -->
                    <div id="reviews" class="tab-content">
                        <div class="reviews-content">
                            <div class="reviews-header">
                                <h3>Customer Reviews</h3>
                                <?php if ($ratingStats['review_count'] > 0): ?>
                                    <div class="rating-breakdown">
                                        <div class="overall-rating">
                                            <div class="rating-score-large">
                                                <?php echo number_format($ratingStats['average_rating'], 1); ?>
                                            </div>
                                            <div class="rating-stars-large">
                                                <?php
                                                $rating = round($ratingStats['average_rating']);
                                                for ($i = 1; $i <= 5; $i++):
                                                    echo $i <= $rating ? '⭐' : '☆';
                                                endfor;
                                                ?>
                                            </div>
                                            <div class="total-reviews">
                                                Based on <?php echo $ratingStats['review_count']; ?> review<?php echo $ratingStats['review_count'] !== 1 ? 's' : ''; ?>
                                            </div>
                                        </div>

                                        <div class="rating-bars">
                                            <?php for ($i = 5; $i >= 1; $i--): ?>
                                                <?php
                                                $starCount = $ratingStats[['', 'one_star', 'two_star', 'three_star', 'four_star', 'five_star'][$i]];
                                                $percentage = $ratingStats['review_count'] > 0 ? ($starCount / $ratingStats['review_count']) * 100 : 0;
                                                ?>
                                                <div class="rating-bar">
                                                    <span class="star-label"><?php echo $i; ?> ⭐</span>
                                                    <div class="bar-container">
                                                        <div class="bar-fill" style="width: <?php echo $percentage; ?>%"></div>
                                                    </div>
                                                    <span class="star-count"><?php echo $starCount; ?></span>
                                                </div>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <?php if (!empty($reviews)): ?>
                                <div class="reviews-list">
                                    <?php foreach ($reviews as $review): ?>
                                        <div class="review-item">
                                            <div class="review-header">
                                                <div class="reviewer-info">
                                                    <div class="reviewer-name">
                                                        <?php echo htmlspecialchars($review['first_name'] . ' ' . substr($review['last_name'], 0, 1) . '.'); ?>
                                                    </div>
                                                    <div class="review-date">
                                                        <?php echo formatDate($review['created_at']); ?>
                                                    </div>
                                                </div>
                                                <div class="review-rating">
                                                    <?php
                                                    for ($i = 1; $i <= 5; $i++):
                                                        echo $i <= $review['rating'] ? '⭐' : '☆';
                                                    endfor;
                                                    ?>
                                                </div>
                                            </div>
                                            <?php if (!empty($review['comment'])): ?>
                                                <div class="review-comment">
                                                    <?php echo nl2br(htmlspecialchars($review['comment'])); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="no-reviews">
                                    <p>No reviews yet. Be the first to share your experience!</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Booking Tab (for logged-in customers) -->
                    <?php if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'customer'): ?>
                        <div id="booking" class="tab-content">
                            <div class="booking-content">
                                <h3>Book an Appointment</h3>
                                <p>Select a service below to book your appointment:</p>

                                <?php if (!empty($servicesByCategory)): ?>
                                    <div class="booking-services">
                                        <?php foreach ($servicesByCategory as $category => $categoryServices): ?>
                                            <div class="booking-category">
                                                <h4><?php echo htmlspecialchars($category); ?></h4>
                                                <div class="booking-services-list">
                                                    <?php foreach ($categoryServices as $service): ?>
                                                        <div class="booking-service-item">
                                                            <div class="service-details">
                                                                <h5><?php echo htmlspecialchars($service['name']); ?></h5>
                                                                <p><?php echo htmlspecialchars($service['description']); ?></p>
                                                                <div class="service-meta">
                                                                    <span>⏱️ <?php echo $service['duration_minutes']; ?> min</span>
                                                                    <span>💰 <?php echo formatPrice($service['price']); ?></span>
                                                                </div>
                                                            </div>
                                                            <div class="service-book">
                                                                <a href="booking.php?service=<?php echo $service['id']; ?>"
                                                                    class="btn btn-primary">
                                                                    Book This Service
                                                                </a>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p>No services are currently available for booking.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Beauty Platform</h3>
                    <p>Connecting you with the best beauty and hair professionals in your area.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="register.php">Join as Shop Owner</a></li>
                        <li><a href="login.php">Login</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#contact">Contact Us</a></li>
                        <li><a href="#help">Help Center</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> Beauty Platform. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tab buttons
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => btn.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Branch switching functionality
        function switchBranch(branchId) {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('branch', branchId);
            window.location.href = currentUrl.toString();
        }

        // Service booking functionality
        function bookService(serviceId) {
            <?php if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'customer'): ?>
                window.location.href = 'booking.php?service=' + serviceId;
            <?php else: ?>
                window.location.href = 'login.php?redirect=' + encodeURIComponent(window.location.href);
            <?php endif; ?>
        }
    </script>
</body>

</html>