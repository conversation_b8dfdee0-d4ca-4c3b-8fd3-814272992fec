<?php
/**
 * Database Backup and Recovery Script
 * Handles database backup creation and restoration
 */

require_once 'config.php';

class DatabaseBackup {
    private $pdo;
    private $backupDir;
    private $errors = [];
    private $success = [];
    
    public function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ];
            
            $this->pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // Create backup directory if it doesn't exist
            $this->backupDir = __DIR__ . '/../backups/';
            if (!is_dir($this->backupDir)) {
                mkdir($this->backupDir, 0755, true);
            }
            
        } catch (PDOException $e) {
            $this->errors[] = "Database connection failed: " . $e->getMessage();
        }
    }
    
    /**
     * Create a complete database backup
     */
    public function createBackup($filename = null) {
        if (!empty($this->errors)) {
            return false;
        }
        
        if ($filename === null) {
            $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $backupPath = $this->backupDir . $filename;
        
        try {
            $backup = $this->generateBackupSQL();
            
            if (file_put_contents($backupPath, $backup) !== false) {
                $this->success[] = "Backup created successfully: " . $filename;
                return $backupPath;
            } else {
                $this->errors[] = "Failed to write backup file: " . $backupPath;
                return false;
            }
            
        } catch (Exception $e) {
            $this->errors[] = "Backup creation failed: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Generate SQL backup content
     */
    private function generateBackupSQL() {
        $backup = "-- Beauty Platform Database Backup\n";
        $backup .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $backup .= "-- Database: " . DB_NAME . "\n\n";
        
        $backup .= "SET FOREIGN_KEY_CHECKS = 0;\n";
        $backup .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $backup .= "SET AUTOCOMMIT = 0;\n";
        $backup .= "START TRANSACTION;\n\n";
        
        // Get all tables
        $tables = $this->getTables();
        
        foreach ($tables as $table) {
            $backup .= $this->backupTable($table);
        }
        
        $backup .= "\nSET FOREIGN_KEY_CHECKS = 1;\n";
        $backup .= "COMMIT;\n";
        
        return $backup;
    }
    
    /**
     * Get all tables in the database
     */
    private function getTables() {
        $stmt = $this->pdo->query("SHOW TABLES");
        $tables = [];
        
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }
        
        return $tables;
    }
    
    /**
     * Backup a single table
     */
    private function backupTable($tableName) {
        $backup = "-- Table structure for `{$tableName}`\n";
        $backup .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
        
        // Get table structure
        $stmt = $this->pdo->query("SHOW CREATE TABLE `{$tableName}`");
        $row = $stmt->fetch();
        $backup .= $row['Create Table'] . ";\n\n";
        
        // Get table data
        $stmt = $this->pdo->query("SELECT * FROM `{$tableName}`");
        $rowCount = $stmt->rowCount();
        
        if ($rowCount > 0) {
            $backup .= "-- Dumping data for table `{$tableName}`\n";
            $backup .= "LOCK TABLES `{$tableName}` WRITE;\n";
            
            // Get column names
            $columns = [];
            for ($i = 0; $i < $stmt->columnCount(); $i++) {
                $meta = $stmt->getColumnMeta($i);
                $columns[] = '`' . $meta['name'] . '`';
            }
            
            $backup .= "INSERT INTO `{$tableName}` (" . implode(', ', $columns) . ") VALUES\n";
            
            $values = [];
            while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
                $escapedRow = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $escapedRow[] = 'NULL';
                    } else {
                        $escapedRow[] = $this->pdo->quote($value);
                    }
                }
                $values[] = '(' . implode(', ', $escapedRow) . ')';
            }
            
            $backup .= implode(",\n", $values) . ";\n";
            $backup .= "UNLOCK TABLES;\n\n";
        } else {
            $backup .= "-- No data to dump for table `{$tableName}`\n\n";
        }
        
        return $backup;
    }
    
    /**
     * Restore database from backup file
     */
    public function restoreBackup($backupFile) {
        if (!file_exists($backupFile)) {
            $this->errors[] = "Backup file not found: " . $backupFile;
            return false;
        }
        
        try {
            $sql = file_get_contents($backupFile);
            
            if ($sql === false) {
                $this->errors[] = "Failed to read backup file: " . $backupFile;
                return false;
            }
            
            // Split SQL into individual statements
            $statements = array_filter(
                array_map('trim', explode(';', $sql)),
                function($stmt) {
                    return !empty($stmt) && !preg_match('/^--/', $stmt);
                }
            );
            
            $this->pdo->beginTransaction();
            
            foreach ($statements as $statement) {
                if (!empty(trim($statement))) {
                    $this->pdo->exec($statement);
                }
            }
            
            $this->pdo->commit();
            $this->success[] = "Database restored successfully from: " . basename($backupFile);
            return true;
            
        } catch (PDOException $e) {
            $this->pdo->rollback();
            $this->errors[] = "Restore failed: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * List available backup files
     */
    public function listBackups() {
        $backups = [];
        
        if (is_dir($this->backupDir)) {
            $files = scandir($this->backupDir);
            
            foreach ($files as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
                    $filePath = $this->backupDir . $file;
                    $backups[] = [
                        'filename' => $file,
                        'path' => $filePath,
                        'size' => filesize($filePath),
                        'created' => filemtime($filePath)
                    ];
                }
            }
            
            // Sort by creation time (newest first)
            usort($backups, function($a, $b) {
                return $b['created'] - $a['created'];
            });
        }
        
        return $backups;
    }
    
    /**
     * Delete old backup files (keep only specified number)
     */
    public function cleanupBackups($keepCount = 10) {
        $backups = $this->listBackups();
        $deleted = 0;
        
        if (count($backups) > $keepCount) {
            $toDelete = array_slice($backups, $keepCount);
            
            foreach ($toDelete as $backup) {
                if (unlink($backup['path'])) {
                    $deleted++;
                }
            }
            
            $this->success[] = "Cleaned up {$deleted} old backup files";
        }
        
        return $deleted;
    }
    
    /**
     * Create automated backup with cleanup
     */
    public function createAutomatedBackup() {
        $backupPath = $this->createBackup();
        
        if ($backupPath) {
            $this->cleanupBackups(10); // Keep only 10 most recent backups
            return $backupPath;
        }
        
        return false;
    }
    
    /**
     * Get backup statistics
     */
    public function getBackupStats() {
        $backups = $this->listBackups();
        $totalSize = 0;
        
        foreach ($backups as $backup) {
            $totalSize += $backup['size'];
        }
        
        return [
            'count' => count($backups),
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'latest' => !empty($backups) ? $backups[0] : null
        ];
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Get errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get success messages
     */
    public function getSuccess() {
        return $this->success;
    }
    
    /**
     * Display results
     */
    public function displayResults() {
        if (!empty($this->success)) {
            echo "<div style='color: green; margin: 10px 0;'>\n";
            echo "<h3>✓ Success</h3>\n";
            foreach ($this->success as $message) {
                echo "<p>✓ " . htmlspecialchars($message) . "</p>\n";
            }
            echo "</div>\n";
        }
        
        if (!empty($this->errors)) {
            echo "<div style='color: red; margin: 10px 0;'>\n";
            echo "<h3>✗ Errors</h3>\n";
            foreach ($this->errors as $error) {
                echo "<p>✗ " . htmlspecialchars($error) . "</p>\n";
            }
            echo "</div>\n";
        }
    }
}

// Command line interface for backup operations
if (basename($_SERVER['PHP_SELF']) === 'backup.php' && php_sapi_name() === 'cli') {
    $backup = new DatabaseBackup();
    
    if ($argc < 2) {
        echo "Usage: php backup.php [create|restore|list|cleanup|stats]\n";
        echo "  create [filename]  - Create a new backup\n";
        echo "  restore <filename> - Restore from backup file\n";
        echo "  list              - List available backups\n";
        echo "  cleanup [count]   - Clean up old backups (default: keep 10)\n";
        echo "  stats             - Show backup statistics\n";
        exit(1);
    }
    
    $command = $argv[1];
    
    switch ($command) {
        case 'create':
            $filename = isset($argv[2]) ? $argv[2] : null;
            $result = $backup->createBackup($filename);
            if ($result) {
                echo "Backup created: " . $result . "\n";
            }
            break;
            
        case 'restore':
            if (!isset($argv[2])) {
                echo "Error: Please specify backup filename\n";
                exit(1);
            }
            $backupFile = $argv[2];
            if (!file_exists($backupFile)) {
                $backupFile = __DIR__ . '/../backups/' . $backupFile;
            }
            $backup->restoreBackup($backupFile);
            break;
            
        case 'list':
            $backups = $backup->listBackups();
            if (empty($backups)) {
                echo "No backups found\n";
            } else {
                echo "Available backups:\n";
                foreach ($backups as $b) {
                    echo sprintf("  %s (%s) - %s\n", 
                        $b['filename'], 
                        $backup->formatBytes($b['size']), 
                        date('Y-m-d H:i:s', $b['created'])
                    );
                }
            }
            break;
            
        case 'cleanup':
            $keepCount = isset($argv[2]) ? (int)$argv[2] : 10;
            $deleted = $backup->cleanupBackups($keepCount);
            echo "Cleaned up {$deleted} old backup files\n";
            break;
            
        case 'stats':
            $stats = $backup->getBackupStats();
            echo "Backup Statistics:\n";
            echo "  Total backups: " . $stats['count'] . "\n";
            echo "  Total size: " . $stats['total_size_formatted'] . "\n";
            if ($stats['latest']) {
                echo "  Latest backup: " . $stats['latest']['filename'] . 
                     " (" . date('Y-m-d H:i:s', $stats['latest']['created']) . ")\n";
            }
            break;
            
        default:
            echo "Unknown command: {$command}\n";
            exit(1);
    }
    
    $backup->displayResults();
}
?>