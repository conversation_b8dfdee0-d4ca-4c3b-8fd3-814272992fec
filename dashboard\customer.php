<?php
/**
 * Customer Dashboard - Beauty Platform
 * Displays booking history, upcoming appointments, and allows appointment management
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require customer authentication
requireRole('customer', '/login.php');

// Initialize database query helper
$db = new DatabaseQuery();
$currentUser = getCurrentUser();

// Handle appointment cancellation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'cancel_appointment') {
    $cancellationResult = handleAppointmentCancellation($db, $_POST);
    
    if ($cancellationResult['success']) {
        setFlashMessage('success', $cancellationResult['message']);
    } else {
        setFlashMessage('error', $cancellationResult['message']);
    }
    
    // Redirect to prevent form resubmission
    redirect('/dashboard/customer.php');
}

try {
    // Get upcoming appointments
    $upcomingAppointments = getCustomerUpcomingAppointments($db, $currentUser['id']);
    
    // Get past appointments
    $pastAppointments = getCustomerPastAppointments($db, $currentUser['id']);
    
    // Get appointment statistics
    $appointmentStats = getCustomerAppointmentStats($db, $currentUser['id']);

} catch (Exception $e) {
    logError("Failed to load customer dashboard: " . $e->getMessage(), [
        'user_id' => $currentUser['id']
    ]);
    setFlashMessage('error', 'Unable to load dashboard. Please try again.');
    $upcomingAppointments = [];
    $pastAppointments = [];
    $appointmentStats = [
        'total' => 0,
        'upcoming' => 0,
        'completed' => 0,
        'cancelled' => 0
    ];
}

/**
 * Handle appointment cancellation
 */
function handleAppointmentCancellation($db, $postData) {
    try {
        // Validate CSRF token
        if (!isset($postData['csrf_token']) || !verifyCsrfToken($postData['csrf_token'])) {
            return [
                'success' => false,
                'message' => 'Invalid form submission. Please try again.'
            ];
        }

        $appointmentId = isset($postData['appointment_id']) ? (int)$postData['appointment_id'] : null;
        $cancellationReason = isset($postData['cancellation_reason']) ? sanitizeText($postData['cancellation_reason']) : '';

        if (!$appointmentId || !validateId($appointmentId)) {
            return [
                'success' => false,
                'message' => 'Invalid appointment selected.'
            ];
        }

        // Get appointment details
        $appointment = $db->queryOne("
            SELECT 
                a.*,
                s.name as service_name,
                sb.branch_name,
                sh.business_name
            FROM appointments a
            INNER JOIN services s ON a.service_id = s.id
            INNER JOIN shop_branches sb ON s.branch_id = sb.id
            INNER JOIN shops sh ON sb.shop_id = sh.id
            WHERE a.id = :appointment_id 
            AND a.customer_id = :customer_id
        ", [
            ':appointment_id' => $appointmentId,
            ':customer_id' => getCurrentUserId()
        ]);

        if (!$appointment) {
            return [
                'success' => false,
                'message' => 'Appointment not found.'
            ];
        }

        // Check if appointment can be cancelled
        $cancellationCheck = canCancelAppointment($appointment);
        if (!$cancellationCheck['can_cancel']) {
            return [
                'success' => false,
                'message' => $cancellationCheck['reason']
            ];
        }

        // Begin transaction
        $db->beginTransaction();

        // Update appointment status
        $updateResult = $db->update('appointments', [
            'status' => 'cancelled',
            'cancellation_reason' => $cancellationReason,
            'updated_at' => date('Y-m-d H:i:s')
        ], [
            'id' => $appointmentId
        ]);

        if (!$updateResult) {
            $db->rollback();
            return [
                'success' => false,
                'message' => 'Failed to cancel appointment. Please try again.'
            ];
        }

        // Commit transaction
        $db->commit();

        // Log cancellation
        logSecurityEvent('appointment_cancelled', [
            'appointment_id' => $appointmentId,
            'customer_id' => getCurrentUserId(),
            'cancellation_reason' => $cancellationReason,
            'appointment_date' => $appointment['appointment_date'],
            'appointment_time' => $appointment['appointment_time']
        ]);

        return [
            'success' => true,
            'message' => 'Appointment cancelled successfully.'
        ];

    } catch (Exception $e) {
        if (isset($db)) {
            $db->rollback();
        }
        logError('Appointment cancellation error: ' . $e->getMessage(), $postData);
        return [
            'success' => false,
            'message' => 'Failed to cancel appointment. Please try again.'
        ];
    }
}

/**
 * Check if appointment can be cancelled within policy timeframe
 */
function canCancelAppointment($appointment) {
    // Check if appointment is in a cancellable status
    if (!in_array($appointment['status'], ['pending', 'confirmed'])) {
        return [
            'can_cancel' => false,
            'reason' => 'This appointment cannot be cancelled due to its current status.'
        ];
    }

    // Check if appointment is in the past
    $appointmentDateTime = strtotime($appointment['appointment_date'] . ' ' . $appointment['appointment_time']);
    $currentTime = time();

    if ($appointmentDateTime <= $currentTime) {
        return [
            'can_cancel' => false,
            'reason' => 'Cannot cancel appointments that have already passed.'
        ];
    }

    // Check cancellation policy (24 hours before appointment)
    $cancellationDeadline = $appointmentDateTime - (24 * 60 * 60); // 24 hours before
    
    if ($currentTime > $cancellationDeadline) {
        return [
            'can_cancel' => false,
            'reason' => 'Appointments can only be cancelled at least 24 hours in advance.'
        ];
    }

    return [
        'can_cancel' => true,
        'reason' => ''
    ];
}

/**
 * Get customer's upcoming appointments
 */
function getCustomerUpcomingAppointments($db, $customerId) {
    $sql = "
        SELECT 
            a.*,
            s.name as service_name,
            s.description as service_description,
            s.category as service_category,
            sb.branch_name,
            sb.address,
            sb.city,
            sb.country,
            sb.phone as branch_phone,
            sh.business_name,
            sh.id as shop_id,
            st.name as staff_name
        FROM appointments a
        INNER JOIN services s ON a.service_id = s.id
        INNER JOIN shop_branches sb ON s.branch_id = sb.id
        INNER JOIN shops sh ON sb.shop_id = sh.id
        LEFT JOIN staff st ON a.staff_id = st.id
        WHERE a.customer_id = :customer_id
        AND (
            a.appointment_date > CURDATE() 
            OR (a.appointment_date = CURDATE() AND a.appointment_time > CURTIME())
        )
        AND a.status IN ('pending', 'confirmed')
        ORDER BY a.appointment_date ASC, a.appointment_time ASC
    ";

    return $db->query($sql, [':customer_id' => $customerId]);
}

/**
 * Get customer's past appointments
 */
function getCustomerPastAppointments($db, $customerId) {
    $sql = "
        SELECT 
            a.*,
            s.name as service_name,
            s.description as service_description,
            s.category as service_category,
            sb.branch_name,
            sb.address,
            sb.city,
            sb.country,
            sh.business_name,
            sh.id as shop_id,
            st.name as staff_name,
            r.id as review_id,
            r.rating,
            r.comment as review_comment
        FROM appointments a
        INNER JOIN services s ON a.service_id = s.id
        INNER JOIN shop_branches sb ON s.branch_id = sb.id
        INNER JOIN shops sh ON sb.shop_id = sh.id
        LEFT JOIN staff st ON a.staff_id = st.id
        LEFT JOIN reviews r ON a.id = r.appointment_id AND r.customer_id = a.customer_id
        WHERE a.customer_id = :customer_id
        AND (
            a.appointment_date < CURDATE() 
            OR (a.appointment_date = CURDATE() AND a.appointment_time <= CURTIME())
            OR a.status IN ('completed', 'cancelled', 'no_show')
        )
        ORDER BY a.appointment_date DESC, a.appointment_time DESC
        LIMIT 20
    ";

    return $db->query($sql, [':customer_id' => $customerId]);
}

/**
 * Get customer appointment statistics
 */
function getCustomerAppointmentStats($db, $customerId) {
    $stats = [
        'total' => 0,
        'upcoming' => 0,
        'completed' => 0,
        'cancelled' => 0
    ];

    // Total appointments
    $totalResult = $db->queryOne("
        SELECT COUNT(*) as count 
        FROM appointments 
        WHERE customer_id = :customer_id
    ", [':customer_id' => $customerId]);
    $stats['total'] = (int)$totalResult['count'];

    // Upcoming appointments
    $upcomingResult = $db->queryOne("
        SELECT COUNT(*) as count 
        FROM appointments 
        WHERE customer_id = :customer_id
        AND (
            appointment_date > CURDATE() 
            OR (appointment_date = CURDATE() AND appointment_time > CURTIME())
        )
        AND status IN ('pending', 'confirmed')
    ", [':customer_id' => $customerId]);
    $stats['upcoming'] = (int)$upcomingResult['count'];

    // Completed appointments
    $completedResult = $db->queryOne("
        SELECT COUNT(*) as count 
        FROM appointments 
        WHERE customer_id = :customer_id
        AND status = 'completed'
    ", [':customer_id' => $customerId]);
    $stats['completed'] = (int)$completedResult['count'];

    // Cancelled appointments
    $cancelledResult = $db->queryOne("
        SELECT COUNT(*) as count 
        FROM appointments 
        WHERE customer_id = :customer_id
        AND status = 'cancelled'
    ", [':customer_id' => $customerId]);
    $stats['cancelled'] = (int)$cancelledResult['count'];

    return $stats;
}

$pageTitle = 'Customer Dashboard';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Beauty Platform</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="customer-dashboard">
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="header-content">
                <div class="logo">
                    <h1><a href="../index.php">Beauty Platform</a></h1>
                </div>
                <nav class="nav">
                    <a href="../index.php" class="nav-link">Browse Shops</a>
                    <a href="customer.php" class="nav-link active">Dashboard</a>
                    <a href="../logout.php" class="nav-link">Logout</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Dashboard Header -->
        <section class="dashboard-header">
            <div class="container">
                <div class="dashboard-header-content">
                    <div class="welcome-section">
                        <h1>Welcome back, <?php echo htmlspecialchars($currentUser['name']); ?>!</h1>
                        <p>Manage your appointments and booking history</p>
                    </div>
                    <div class="quick-actions">
                        <a href="../index.php" class="btn btn-primary">Book New Appointment</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Flash Messages -->
        <?php
        $flashMessages = getFlashMessages();
        if (!empty($flashMessages)):
        ?>
            <div class="container">
                <?php foreach ($flashMessages as $message): ?>
                    <div class="alert alert-<?php echo $message['type']; ?>">
                        <?php echo htmlspecialchars($message['message']); ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Dashboard Statistics -->
        <section class="dashboard-stats">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📅</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $appointmentStats['total']; ?></div>
                            <div class="stat-label">Total Appointments</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏰</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $appointmentStats['upcoming']; ?></div>
                            <div class="stat-label">Upcoming</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $appointmentStats['completed']; ?></div>
                            <div class="stat-label">Completed</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">❌</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $appointmentStats['cancelled']; ?></div>
                            <div class="stat-label">Cancelled</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Upcoming Appointments -->
        <section class="appointments-section">
            <div class="container">
                <div class="section-header">
                    <h2>Upcoming Appointments</h2>
                    <?php if (empty($upcomingAppointments)): ?>
                        <a href="../index.php" class="btn btn-primary">Book Your First Appointment</a>
                    <?php endif; ?>
                </div>

                <?php if (!empty($upcomingAppointments)): ?>
                    <div class="appointments-grid">
                        <?php foreach ($upcomingAppointments as $appointment): ?>
                            <div class="appointment-card upcoming">
                                <div class="appointment-header">
                                    <div class="appointment-date">
                                        <div class="date-day"><?php echo date('j', strtotime($appointment['appointment_date'])); ?></div>
                                        <div class="date-month"><?php echo date('M', strtotime($appointment['appointment_date'])); ?></div>
                                    </div>
                                    <div class="appointment-time">
                                        <?php echo formatTime($appointment['appointment_time']); ?>
                                    </div>
                                    <div class="appointment-status status-<?php echo $appointment['status']; ?>">
                                        <?php echo ucfirst($appointment['status']); ?>
                                    </div>
                                </div>

                                <div class="appointment-details">
                                    <h3 class="service-name"><?php echo htmlspecialchars($appointment['service_name']); ?></h3>
                                    <div class="shop-info">
                                        <h4><?php echo htmlspecialchars($appointment['business_name']); ?></h4>
                                        <p class="branch-name"><?php echo htmlspecialchars($appointment['branch_name']); ?></p>
                                        <p class="address">
                                            <?php echo htmlspecialchars($appointment['address']); ?><br>
                                            <?php echo htmlspecialchars($appointment['city']); ?>, <?php echo htmlspecialchars($appointment['country']); ?>
                                        </p>
                                        <?php if (!empty($appointment['branch_phone'])): ?>
                                            <p class="phone">📞 <?php echo htmlspecialchars($appointment['branch_phone']); ?></p>
                                        <?php endif; ?>
                                    </div>

                                    <?php if (!empty($appointment['staff_name'])): ?>
                                        <div class="staff-info">
                                            <span class="staff-label">Staff:</span>
                                            <span class="staff-name"><?php echo htmlspecialchars($appointment['staff_name']); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <div class="appointment-meta">
                                        <div class="duration">
                                            <span class="icon">⏱️</span>
                                            <span><?php echo $appointment['duration_minutes']; ?> minutes</span>
                                        </div>
                                        <div class="price">
                                            <span class="icon">💰</span>
                                            <span><?php echo formatPrice($appointment['total_price']); ?></span>
                                        </div>
                                    </div>

                                    <?php if (!empty($appointment['notes'])): ?>
                                        <div class="appointment-notes">
                                            <strong>Notes:</strong> <?php echo htmlspecialchars($appointment['notes']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="appointment-actions">
                                    <a href="../shop.php?id=<?php echo $appointment['shop_id']; ?>" class="btn btn-secondary btn-sm">
                                        View Shop
                                    </a>
                                    
                                    <?php 
                                    $cancellationCheck = canCancelAppointment($appointment);
                                    if ($cancellationCheck['can_cancel']): 
                                    ?>
                                        <button type="button" 
                                                class="btn btn-danger btn-sm" 
                                                onclick="showCancelModal(<?php echo $appointment['id']; ?>, '<?php echo htmlspecialchars($appointment['service_name'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($appointment['business_name'], ENT_QUOTES); ?>')">
                                            Cancel
                                        </button>
                                    <?php else: ?>
                                        <span class="cancel-disabled" title="<?php echo htmlspecialchars($cancellationCheck['reason']); ?>">
                                            Cannot Cancel
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-icon">📅</div>
                        <h3>No Upcoming Appointments</h3>
                        <p>You don't have any upcoming appointments. Book one now to get started!</p>
                        <a href="../index.php" class="btn btn-primary">Browse Beauty Shops</a>
                    </div>
                <?php endif; ?>
            </div>
        </section>

        <!-- Past Appointments -->
        <section class="appointments-section">
            <div class="container">
                <div class="section-header">
                    <h2>Past Appointments</h2>
                </div>

                <?php if (!empty($pastAppointments)): ?>
                    <div class="appointments-grid">
                        <?php foreach ($pastAppointments as $appointment): ?>
                            <div class="appointment-card past">
                                <div class="appointment-header">
                                    <div class="appointment-date">
                                        <div class="date-day"><?php echo date('j', strtotime($appointment['appointment_date'])); ?></div>
                                        <div class="date-month"><?php echo date('M Y', strtotime($appointment['appointment_date'])); ?></div>
                                    </div>
                                    <div class="appointment-time">
                                        <?php echo formatTime($appointment['appointment_time']); ?>
                                    </div>
                                    <div class="appointment-status status-<?php echo $appointment['status']; ?>">
                                        <?php echo ucfirst($appointment['status']); ?>
                                    </div>
                                </div>

                                <div class="appointment-details">
                                    <h3 class="service-name"><?php echo htmlspecialchars($appointment['service_name']); ?></h3>
                                    <div class="shop-info">
                                        <h4><?php echo htmlspecialchars($appointment['business_name']); ?></h4>
                                        <p class="branch-name"><?php echo htmlspecialchars($appointment['branch_name']); ?></p>
                                    </div>

                                    <?php if (!empty($appointment['staff_name'])): ?>
                                        <div class="staff-info">
                                            <span class="staff-label">Staff:</span>
                                            <span class="staff-name"><?php echo htmlspecialchars($appointment['staff_name']); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <div class="appointment-meta">
                                        <div class="duration">
                                            <span class="icon">⏱️</span>
                                            <span><?php echo $appointment['duration_minutes']; ?> minutes</span>
                                        </div>
                                        <div class="price">
                                            <span class="icon">💰</span>
                                            <span><?php echo formatPrice($appointment['total_price']); ?></span>
                                        </div>
                                    </div>

                                    <?php if ($appointment['status'] === 'cancelled' && !empty($appointment['cancellation_reason'])): ?>
                                        <div class="cancellation-reason">
                                            <strong>Cancellation reason:</strong> <?php echo htmlspecialchars($appointment['cancellation_reason']); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($appointment['review_id'])): ?>
                                        <div class="review-info">
                                            <div class="rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <span class="star <?php echo $i <= $appointment['rating'] ? 'filled' : ''; ?>">★</span>
                                                <?php endfor; ?>
                                            </div>
                                            <?php if (!empty($appointment['review_comment'])): ?>
                                                <p class="review-comment"><?php echo htmlspecialchars($appointment['review_comment']); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="appointment-actions">
                                    <a href="../shop.php?id=<?php echo $appointment['shop_id']; ?>" class="btn btn-secondary btn-sm">
                                        View Shop
                                    </a>
                                    
                                    <?php if ($appointment['status'] === 'completed' && empty($appointment['review_id'])): ?>
                                        <a href="../review.php?appointment=<?php echo $appointment['id']; ?>" class="btn btn-primary btn-sm">
                                            Leave Review
                                        </a>
                                    <?php endif; ?>
                                    
                                    <a href="../booking.php?service=<?php echo $appointment['service_id']; ?>" class="btn btn-outline btn-sm">
                                        Book Again
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-icon">📋</div>
                        <h3>No Past Appointments</h3>
                        <p>Your appointment history will appear here once you've had some appointments.</p>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Beauty Platform. All rights reserved.</p>
        </div>
    </footer>

    <!-- Cancel Appointment Modal -->
    <div id="cancelModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Cancel Appointment</h3>
                <button type="button" class="modal-close" onclick="hideCancelModal()">&times;</button>
            </div>
            
            <form method="POST" id="cancelForm">
                <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
                <input type="hidden" name="action" value="cancel_appointment">
                <input type="hidden" name="appointment_id" id="cancelAppointmentId">
                
                <div class="modal-body">
                    <p>Are you sure you want to cancel your appointment for <strong id="cancelServiceName"></strong> at <strong id="cancelShopName"></strong>?</p>
                    
                    <div class="form-group">
                        <label for="cancellation_reason">Reason for cancellation (optional):</label>
                        <textarea id="cancellation_reason" 
                                  name="cancellation_reason" 
                                  rows="3" 
                                  maxlength="500"
                                  placeholder="Please let us know why you're cancelling..."></textarea>
                    </div>
                    
                    <div class="cancellation-policy">
                        <p><strong>Cancellation Policy:</strong> Appointments can be cancelled up to 24 hours before the scheduled time. Late cancellations may be subject to fees.</p>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideCancelModal()">
                        Keep Appointment
                    </button>
                    <button type="submit" class="btn btn-danger">
                        Cancel Appointment
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Cancel appointment modal functions
        function showCancelModal(appointmentId, serviceName, shopName) {
            document.getElementById('cancelAppointmentId').value = appointmentId;
            document.getElementById('cancelServiceName').textContent = serviceName;
            document.getElementById('cancelShopName').textContent = shopName;
            document.getElementById('cancelModal').style.display = 'flex';
        }

        function hideCancelModal() {
            document.getElementById('cancelModal').style.display = 'none';
            document.getElementById('cancelForm').reset();
        }

        // Close modal when clicking outside
        document.getElementById('cancelModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideCancelModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideCancelModal();
            }
        });
    </script>
</body>
</html>