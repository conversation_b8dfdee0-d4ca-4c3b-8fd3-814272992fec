<?php
/**
 * Database Installation Script
 * Creates database, tables, and inserts initial data
 */

require_once 'config.php';

class DatabaseInstaller {
    private $pdo;
    private $errors = [];
    private $success = [];
    
    public function __construct() {
        // Connect without specifying database first
        try {
            $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ];
            
            $this->pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            $this->errors[] = "Database connection failed: " . $e->getMessage();
        }
    }
    
    /**
     * Run complete installation process
     */
    public function install() {
        echo "<h2>Beauty Platform Database Installation</h2>\n";
        
        if (!empty($this->errors)) {
            $this->displayErrors();
            return false;
        }
        
        // Step 1: Create database
        if ($this->createDatabase()) {
            $this->success[] = "Database created successfully";
        }
        
        // Step 2: Create tables
        if ($this->createTables()) {
            $this->success[] = "Tables created successfully";
        }
        
        // Step 3: Insert sample data
        if ($this->insertSampleData()) {
            $this->success[] = "Sample data inserted successfully";
        }
        
        // Step 4: Create indexes
        if ($this->createIndexes()) {
            $this->success[] = "Indexes created successfully";
        }
        
        $this->displayResults();
        return empty($this->errors);
    }
    
    /**
     * Create database if it doesn't exist
     */
    private function createDatabase() {
        try {
            $sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $this->pdo->exec($sql);
            
            // Switch to the created database
            $this->pdo->exec("USE " . DB_NAME);
            return true;
        } catch (PDOException $e) {
            $this->errors[] = "Failed to create database: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Create all required tables
     */
    private function createTables() {
        // Try MariaDB-compatible schema first, fallback to original
        $schemaFiles = [
            __DIR__ . '/schema_mariadb.sql',
            __DIR__ . '/schema.sql'
        ];
        
        $schemaFile = null;
        foreach ($schemaFiles as $file) {
            if (file_exists($file)) {
                $schemaFile = $file;
                break;
            }
        }
        
        if (!$schemaFile) {
            $this->errors[] = "No schema file found";
            return false;
        }
        
        try {
            // Create tables individually for better error handling
            $this->createUsersTable();
            $this->createShopsTable();
            $this->createShopBranchesTable();
            $this->createServicesTable();
            $this->createStaffTable();
            $this->createAppointmentsTable();
            $this->createReviewsTable();
            $this->createAdminLogsTable();
            $this->createPasswordResetTokensTable();
            $this->createUserSessionsTable();
            
            return true;
        } catch (PDOException $e) {
            $this->errors[] = "Failed to create tables: " . $e->getMessage();
            return false;
        }
    }
    
    private function createUsersTable() {
        $sql = "CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            role ENUM('customer', 'shop_owner', 'admin') NOT NULL DEFAULT 'customer',
            status ENUM('active', 'suspended', 'pending') DEFAULT 'active',
            email_verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_role (role),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function createShopsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS shops (
            id INT PRIMARY KEY AUTO_INCREMENT,
            owner_id INT NOT NULL,
            business_name VARCHAR(255) NOT NULL,
            description TEXT,
            business_license VARCHAR(255),
            status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
            rejection_reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_owner (owner_id),
            INDEX idx_status (status),
            INDEX idx_business_name (business_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function createShopBranchesTable() {
        $sql = "CREATE TABLE IF NOT EXISTS shop_branches (
            id INT PRIMARY KEY AUTO_INCREMENT,
            shop_id INT NOT NULL,
            branch_name VARCHAR(255) NOT NULL,
            address TEXT NOT NULL,
            city VARCHAR(100) NOT NULL,
            country VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            email VARCHAR(255),
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            working_hours JSON,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE,
            INDEX idx_shop (shop_id),
            INDEX idx_location (city, country),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function createServicesTable() {
        $sql = "CREATE TABLE IF NOT EXISTS services (
            id INT PRIMARY KEY AUTO_INCREMENT,
            branch_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            duration_minutes INT NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            category VARCHAR(100),
            image_url VARCHAR(500),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (branch_id) REFERENCES shop_branches(id) ON DELETE CASCADE,
            INDEX idx_branch (branch_id),
            INDEX idx_category (category),
            INDEX idx_status (status),
            INDEX idx_price (price)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function createStaffTable() {
        $sql = "CREATE TABLE IF NOT EXISTS staff (
            id INT PRIMARY KEY AUTO_INCREMENT,
            branch_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            phone VARCHAR(20),
            specialties JSON,
            working_hours JSON,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (branch_id) REFERENCES shop_branches(id) ON DELETE CASCADE,
            INDEX idx_branch (branch_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function createAppointmentsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS appointments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            customer_id INT NOT NULL,
            service_id INT NOT NULL,
            staff_id INT,
            appointment_date DATE NOT NULL,
            appointment_time TIME NOT NULL,
            duration_minutes INT NOT NULL,
            total_price DECIMAL(10, 2) NOT NULL,
            status ENUM('pending', 'confirmed', 'completed', 'cancelled', 'no_show') DEFAULT 'pending',
            notes TEXT,
            cancellation_reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
            FOREIGN KEY (staff_id) REFERENCES staff(id) ON DELETE SET NULL,
            INDEX idx_customer (customer_id),
            INDEX idx_service (service_id),
            INDEX idx_staff (staff_id),
            INDEX idx_date_time (appointment_date, appointment_time),
            INDEX idx_status (status),
            UNIQUE KEY unique_appointment (service_id, staff_id, appointment_date, appointment_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function createReviewsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS reviews (
            id INT PRIMARY KEY AUTO_INCREMENT,
            customer_id INT NOT NULL,
            shop_id INT NOT NULL,
            appointment_id INT,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            comment TEXT,
            status ENUM('active', 'hidden', 'pending') DEFAULT 'pending',
            moderation_reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE,
            FOREIGN KEY (appointment_id) REFERENCES appointments(id) ON DELETE SET NULL,
            INDEX idx_customer (customer_id),
            INDEX idx_shop (shop_id),
            INDEX idx_appointment (appointment_id),
            INDEX idx_rating (rating),
            INDEX idx_status (status),
            UNIQUE KEY unique_review (customer_id, appointment_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function createAdminLogsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS admin_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            admin_id INT NOT NULL,
            action VARCHAR(255) NOT NULL,
            target_type ENUM('user', 'shop', 'review', 'appointment') NOT NULL,
            target_id INT NOT NULL,
            details JSON,
            ip_address VARCHAR(45),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_admin (admin_id),
            INDEX idx_action (action),
            INDEX idx_target (target_type, target_id),
            INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function createPasswordResetTokensTable() {
        $sql = "CREATE TABLE IF NOT EXISTS password_reset_tokens (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            token VARCHAR(255) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            used BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user (user_id),
            INDEX idx_token (token),
            INDEX idx_expires (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function createUserSessionsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS user_sessions (
            id VARCHAR(128) PRIMARY KEY,
            user_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user (user_id),
            INDEX idx_last_activity (last_activity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    /**
     * Insert sample data for testing
     */
    private function insertSampleData() {
        try {
            $this->pdo->beginTransaction();
            
            // Insert sample users
            $this->insertSampleUsers();
            
            // Insert sample shops
            $this->insertSampleShops();
            
            // Insert sample branches
            $this->insertSampleBranches();
            
            // Insert sample services
            $this->insertSampleServices();
            
            // Insert sample staff
            $this->insertSampleStaff();
            
            // Insert sample appointments
            $this->insertSampleAppointments();
            
            // Insert sample reviews
            $this->insertSampleReviews();
            
            $this->pdo->commit();
            return true;
        } catch (PDOException $e) {
            $this->pdo->rollback();
            $this->errors[] = "Failed to insert sample data: " . $e->getMessage();
            return false;
        }
    }
    
    private function insertSampleUsers() {
        $users = [
            [
                'email' => '<EMAIL>',
                'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
                'first_name' => 'Admin',
                'last_name' => 'User',
                'phone' => '+1234567890',
                'role' => 'admin',
                'status' => 'active',
                'email_verified' => 1
            ],
            [
                'email' => '<EMAIL>',
                'password_hash' => password_hash('customer123', PASSWORD_DEFAULT),
                'first_name' => 'John',
                'last_name' => 'Customer',
                'phone' => '+1234567891',
                'role' => 'customer',
                'status' => 'active',
                'email_verified' => 1
            ],
            [
                'email' => '<EMAIL>',
                'password_hash' => password_hash('customer123', PASSWORD_DEFAULT),
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'phone' => '+1234567892',
                'role' => 'customer',
                'status' => 'active',
                'email_verified' => 1
            ],
            [
                'email' => '<EMAIL>',
                'password_hash' => password_hash('owner123', PASSWORD_DEFAULT),
                'first_name' => 'Sarah',
                'last_name' => 'Johnson',
                'phone' => '+1234567893',
                'role' => 'shop_owner',
                'status' => 'active',
                'email_verified' => 1
            ],
            [
                'email' => '<EMAIL>',
                'password_hash' => password_hash('owner123', PASSWORD_DEFAULT),
                'first_name' => 'Michael',
                'last_name' => 'Brown',
                'phone' => '+1234567894',
                'role' => 'shop_owner',
                'status' => 'active',
                'email_verified' => 1
            ]
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT IGNORE INTO users (email, password_hash, first_name, last_name, phone, role, status, email_verified) 
            VALUES (:email, :password_hash, :first_name, :last_name, :phone, :role, :status, :email_verified)
        ");
        
        foreach ($users as $user) {
            $stmt->execute($user);
        }
    }
    
    private function insertSampleShops() {
        $shops = [
            [
                'owner_id' => 4, // Sarah Johnson
                'business_name' => 'Elegant Hair Salon',
                'description' => 'Premium hair salon offering cutting, coloring, and styling services',
                'business_license' => 'BL123456',
                'status' => 'approved'
            ],
            [
                'owner_id' => 5, // Michael Brown
                'business_name' => 'Serenity Spa & Beauty',
                'description' => 'Full-service spa offering beauty treatments, massages, and wellness services',
                'business_license' => 'BL789012',
                'status' => 'approved'
            ]
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO shops (owner_id, business_name, description, business_license, status) 
            VALUES (:owner_id, :business_name, :description, :business_license, :status)
        ");
        
        foreach ($shops as $shop) {
            $stmt->execute($shop);
        }
    }
    
    private function insertSampleBranches() {
        $branches = [
            [
                'shop_id' => 1,
                'branch_name' => 'Elegant Hair Salon - Downtown',
                'address' => '123 Main Street, Suite 100',
                'city' => 'New York',
                'country' => 'USA',
                'phone' => '+**********',
                'email' => '<EMAIL>',
                'latitude' => 40.7128,
                'longitude' => -74.0060,
                'working_hours' => json_encode([
                    'monday' => ['open' => '09:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                    'thursday' => ['open' => '09:00', 'close' => '20:00'],
                    'friday' => ['open' => '09:00', 'close' => '20:00'],
                    'saturday' => ['open' => '08:00', 'close' => '17:00'],
                    'sunday' => ['closed' => true]
                ]),
                'status' => 'active'
            ],
            [
                'shop_id' => 2,
                'branch_name' => 'Serenity Spa - Midtown',
                'address' => '456 Park Avenue, Floor 2',
                'city' => 'New York',
                'country' => 'USA',
                'phone' => '+1234567896',
                'email' => '<EMAIL>',
                'latitude' => 40.7589,
                'longitude' => -73.9851,
                'working_hours' => json_encode([
                    'monday' => ['open' => '10:00', 'close' => '19:00'],
                    'tuesday' => ['open' => '10:00', 'close' => '19:00'],
                    'wednesday' => ['open' => '10:00', 'close' => '19:00'],
                    'thursday' => ['open' => '10:00', 'close' => '21:00'],
                    'friday' => ['open' => '10:00', 'close' => '21:00'],
                    'saturday' => ['open' => '09:00', 'close' => '18:00'],
                    'sunday' => ['open' => '11:00', 'close' => '17:00']
                ]),
                'status' => 'active'
            ]
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO shop_branches (shop_id, branch_name, address, city, country, phone, email, latitude, longitude, working_hours, status) 
            VALUES (:shop_id, :branch_name, :address, :city, :country, :phone, :email, :latitude, :longitude, :working_hours, :status)
        ");
        
        foreach ($branches as $branch) {
            $stmt->execute($branch);
        }
    }
    
    private function insertSampleServices() {
        $services = [
            // Elegant Hair Salon services
            [
                'branch_id' => 1,
                'name' => 'Women\'s Haircut & Style',
                'description' => 'Professional haircut with wash, cut, and styling',
                'duration_minutes' => 60,
                'price' => 75.00,
                'category' => 'Hair',
                'status' => 'active'
            ],
            [
                'branch_id' => 1,
                'name' => 'Hair Coloring',
                'description' => 'Full hair coloring service with premium products',
                'duration_minutes' => 120,
                'price' => 150.00,
                'category' => 'Hair',
                'status' => 'active'
            ],
            [
                'branch_id' => 1,
                'name' => 'Men\'s Haircut',
                'description' => 'Classic men\'s haircut and styling',
                'duration_minutes' => 30,
                'price' => 45.00,
                'category' => 'Hair',
                'status' => 'active'
            ],
            // Serenity Spa services
            [
                'branch_id' => 2,
                'name' => 'Relaxing Facial',
                'description' => 'Deep cleansing facial with moisturizing treatment',
                'duration_minutes' => 90,
                'price' => 120.00,
                'category' => 'Facial',
                'status' => 'active'
            ],
            [
                'branch_id' => 2,
                'name' => 'Swedish Massage',
                'description' => 'Full body relaxing Swedish massage',
                'duration_minutes' => 60,
                'price' => 100.00,
                'category' => 'Massage',
                'status' => 'active'
            ],
            [
                'branch_id' => 2,
                'name' => 'Manicure & Pedicure',
                'description' => 'Complete nail care with polish',
                'duration_minutes' => 75,
                'price' => 65.00,
                'category' => 'Nails',
                'status' => 'active'
            ]
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO services (branch_id, name, description, duration_minutes, price, category, status) 
            VALUES (:branch_id, :name, :description, :duration_minutes, :price, :category, :status)
        ");
        
        foreach ($services as $service) {
            $stmt->execute($service);
        }
    }
    
    private function insertSampleStaff() {
        $staff = [
            [
                'branch_id' => 1,
                'name' => 'Emily Rodriguez',
                'email' => '<EMAIL>',
                'phone' => '+1234567897',
                'specialties' => json_encode(['Hair Cutting', 'Hair Coloring', 'Styling']),
                'working_hours' => json_encode([
                    'monday' => ['start' => '09:00', 'end' => '17:00'],
                    'tuesday' => ['start' => '09:00', 'end' => '17:00'],
                    'wednesday' => ['start' => '09:00', 'end' => '17:00'],
                    'thursday' => ['start' => '10:00', 'end' => '18:00'],
                    'friday' => ['start' => '10:00', 'end' => '18:00'],
                    'saturday' => ['start' => '08:00', 'end' => '16:00']
                ]),
                'status' => 'active'
            ],
            [
                'branch_id' => 1,
                'name' => 'David Kim',
                'email' => '<EMAIL>',
                'phone' => '+1234567898',
                'specialties' => json_encode(['Men\'s Cuts', 'Beard Trimming']),
                'working_hours' => json_encode([
                    'tuesday' => ['start' => '10:00', 'end' => '18:00'],
                    'wednesday' => ['start' => '10:00', 'end' => '18:00'],
                    'thursday' => ['start' => '10:00', 'end' => '20:00'],
                    'friday' => ['start' => '10:00', 'end' => '20:00'],
                    'saturday' => ['start' => '09:00', 'end' => '17:00']
                ]),
                'status' => 'active'
            ],
            [
                'branch_id' => 2,
                'name' => 'Lisa Chen',
                'email' => '<EMAIL>',
                'phone' => '+1234567899',
                'specialties' => json_encode(['Facials', 'Skincare', 'Anti-aging treatments']),
                'working_hours' => json_encode([
                    'monday' => ['start' => '10:00', 'end' => '18:00'],
                    'tuesday' => ['start' => '10:00', 'end' => '18:00'],
                    'wednesday' => ['start' => '10:00', 'end' => '18:00'],
                    'thursday' => ['start' => '11:00', 'end' => '19:00'],
                    'friday' => ['start' => '11:00', 'end' => '19:00']
                ]),
                'status' => 'active'
            ],
            [
                'branch_id' => 2,
                'name' => 'Marcus Thompson',
                'email' => '<EMAIL>',
                'phone' => '+1234567800',
                'specialties' => json_encode(['Massage Therapy', 'Deep Tissue', 'Sports Massage']),
                'working_hours' => json_encode([
                    'monday' => ['start' => '11:00', 'end' => '19:00'],
                    'wednesday' => ['start' => '11:00', 'end' => '19:00'],
                    'thursday' => ['start' => '12:00', 'end' => '20:00'],
                    'friday' => ['start' => '12:00', 'end' => '20:00'],
                    'saturday' => ['start' => '09:00', 'end' => '17:00'],
                    'sunday' => ['start' => '11:00', 'end' => '16:00']
                ]),
                'status' => 'active'
            ]
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO staff (branch_id, name, email, phone, specialties, working_hours, status) 
            VALUES (:branch_id, :name, :email, :phone, :specialties, :working_hours, :status)
        ");
        
        foreach ($staff as $member) {
            $stmt->execute($member);
        }
    }
    
    private function insertSampleAppointments() {
        $appointments = [
            [
                'customer_id' => 2, // John Customer
                'service_id' => 1, // Women's Haircut & Style
                'staff_id' => 1, // Emily Rodriguez
                'appointment_date' => date('Y-m-d', strtotime('+1 day')),
                'appointment_time' => '10:00:00',
                'duration_minutes' => 60,
                'total_price' => 75.00,
                'status' => 'confirmed',
                'notes' => 'First time customer'
            ],
            [
                'customer_id' => 3, // Jane Smith
                'service_id' => 4, // Relaxing Facial
                'staff_id' => 3, // Lisa Chen
                'appointment_date' => date('Y-m-d', strtotime('+2 days')),
                'appointment_time' => '14:00:00',
                'duration_minutes' => 90,
                'total_price' => 120.00,
                'status' => 'confirmed',
                'notes' => 'Sensitive skin'
            ],
            [
                'customer_id' => 2, // John Customer
                'service_id' => 5, // Swedish Massage
                'staff_id' => 4, // Marcus Thompson
                'appointment_date' => date('Y-m-d', strtotime('-1 day')),
                'appointment_time' => '16:00:00',
                'duration_minutes' => 60,
                'total_price' => 100.00,
                'status' => 'completed',
                'notes' => 'Lower back tension'
            ]
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO appointments (customer_id, service_id, staff_id, appointment_date, appointment_time, duration_minutes, total_price, status, notes) 
            VALUES (:customer_id, :service_id, :staff_id, :appointment_date, :appointment_time, :duration_minutes, :total_price, :status, :notes)
        ");
        
        foreach ($appointments as $appointment) {
            $stmt->execute($appointment);
        }
    }
    
    private function insertSampleReviews() {
        $reviews = [
            [
                'customer_id' => 2, // John Customer
                'shop_id' => 2, // Serenity Spa
                'appointment_id' => 3, // Completed massage appointment
                'rating' => 5,
                'comment' => 'Excellent massage! Marcus was very professional and the atmosphere was very relaxing.',
                'status' => 'active'
            ],
            [
                'customer_id' => 3, // Jane Smith
                'shop_id' => 1, // Elegant Hair Salon
                'appointment_id' => null,
                'rating' => 4,
                'comment' => 'Great salon with friendly staff. Will definitely come back!',
                'status' => 'active'
            ]
        ];
        
        $stmt = $this->pdo->prepare("
            INSERT INTO reviews (customer_id, shop_id, appointment_id, rating, comment, status) 
            VALUES (:customer_id, :shop_id, :appointment_id, :rating, :comment, :status)
        ");
        
        foreach ($reviews as $review) {
            $stmt->execute($review);
        }
    }
    
    /**
     * Create additional indexes for performance
     */
    private function createIndexes() {
        try {
            // MariaDB compatible indexes (no WHERE clauses)
            $indexes = [
                "CREATE INDEX IF NOT EXISTS idx_shops_approved ON shops(status)",
                "CREATE INDEX IF NOT EXISTS idx_appointments_upcoming ON appointments(appointment_date, appointment_time, status)",
                "CREATE INDEX IF NOT EXISTS idx_reviews_active ON reviews(shop_id, status)"
            ];
            
            foreach ($indexes as $index) {
                try {
                    $this->pdo->exec($index);
                } catch (PDOException $e) {
                    // Log individual index errors but continue
                    error_log("Index creation warning: " . $e->getMessage());
                }
            }
            
            return true;
        } catch (PDOException $e) {
            $this->errors[] = "Failed to create indexes: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Display installation results
     */
    private function displayResults() {
        if (!empty($this->success)) {
            echo "<div style='color: green; margin: 10px 0;'>\n";
            echo "<h3>✓ Installation Successful</h3>\n";
            foreach ($this->success as $message) {
                echo "<p>✓ " . htmlspecialchars($message) . "</p>\n";
            }
            echo "</div>\n";
        }
        
        if (!empty($this->errors)) {
            $this->displayErrors();
        }
    }
    
    /**
     * Display errors
     */
    private function displayErrors() {
        echo "<div style='color: red; margin: 10px 0;'>\n";
        echo "<h3>✗ Installation Errors</h3>\n";
        foreach ($this->errors as $error) {
            echo "<p>✗ " . htmlspecialchars($error) . "</p>\n";
        }
        echo "</div>\n";
    }
    
    /**
     * Check if installation is needed
     */
    public function isInstallationNeeded() {
        try {
            $this->pdo->exec("USE " . DB_NAME);
            $result = $this->pdo->query("SHOW TABLES LIKE 'users'");
            return $result->rowCount() === 0;
        } catch (PDOException $e) {
            return true; // Database doesn't exist, installation needed
        }
    }
}

// Run installation if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'install.php') {
    $installer = new DatabaseInstaller();
    
    if ($installer->isInstallationNeeded()) {
        $installer->install();
    } else {
        echo "<div style='color: orange; margin: 10px 0;'>\n";
        echo "<h3>⚠ Installation Not Needed</h3>\n";
        echo "<p>Database and tables already exist. If you want to reinstall, please drop the database first.</p>\n";
        echo "</div>\n";
    }
}
?>