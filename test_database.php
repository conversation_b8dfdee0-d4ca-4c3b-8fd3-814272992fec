<?php
/**
 * Database Setup Test Script
 * Tests database connection and basic functionality
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

echo "<h2>Beauty Platform Database Test</h2>\n";

// Test 1: Database Connection
echo "<h3>Test 1: Database Connection</h3>\n";
try {
    $db = new DatabaseQuery();
    echo "<p style='color: green;'>✓ Database connection successful</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    exit;
}

// Test 2: Check if tables exist
echo "<h3>Test 2: Table Structure</h3>\n";
$requiredTables = [
    'users', 'shops', 'shop_branches', 'services', 
    'staff', 'appointments', 'reviews', 'admin_logs',
    'password_reset_tokens', 'user_sessions'
];

$existingTables = [];
try {
    $tables = $db->query("SHOW TABLES");
    foreach ($tables as $table) {
        $existingTables[] = array_values($table)[0];
    }
    
    $missingTables = array_diff($requiredTables, $existingTables);
    
    if (empty($missingTables)) {
        echo "<p style='color: green;'>✓ All required tables exist</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Missing tables: " . implode(', ', $missingTables) . "</p>\n";
        echo "<p>Run the installation script to create missing tables.</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error checking tables: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test 3: Sample Data
echo "<h3>Test 3: Sample Data</h3>\n";
try {
    $userCount = $db->count('users');
    $shopCount = $db->count('shops');
    $serviceCount = $db->count('services');
    
    echo "<p>Users: {$userCount}</p>\n";
    echo "<p>Shops: {$shopCount}</p>\n";
    echo "<p>Services: {$serviceCount}</p>\n";
    
    if ($userCount > 0 && $shopCount > 0 && $serviceCount > 0) {
        echo "<p style='color: green;'>✓ Sample data is present</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ No sample data found. Run installation to add sample data.</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error checking sample data: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test 4: CRUD Operations
echo "<h3>Test 4: CRUD Operations</h3>\n";
try {
    // Test insert
    $testUserId = $db->insert('users', [
        'email' => '<EMAIL>',
        'password_hash' => password_hash('test123', PASSWORD_DEFAULT),
        'first_name' => 'Test',
        'last_name' => 'User',
        'role' => 'customer',
        'status' => 'active'
    ]);
    
    if ($testUserId) {
        echo "<p style='color: green;'>✓ Insert operation successful (ID: {$testUserId})</p>\n";
        
        // Test select
        $testUser = $db->selectOne('users', ['id' => $testUserId]);
        if ($testUser && $testUser['email'] === '<EMAIL>') {
            echo "<p style='color: green;'>✓ Select operation successful</p>\n";
            
            // Test update
            $updated = $db->update('users', 
                ['first_name' => 'Updated Test'], 
                ['id' => $testUserId]
            );
            
            if ($updated > 0) {
                echo "<p style='color: green;'>✓ Update operation successful</p>\n";
            } else {
                echo "<p style='color: red;'>✗ Update operation failed</p>\n";
            }
            
            // Test delete (cleanup)
            $deleted = $db->delete('users', ['id' => $testUserId]);
            if ($deleted > 0) {
                echo "<p style='color: green;'>✓ Delete operation successful</p>\n";
            } else {
                echo "<p style='color: red;'>✗ Delete operation failed</p>\n";
            }
        } else {
            echo "<p style='color: red;'>✗ Select operation failed</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ Insert operation failed</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ CRUD test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test 5: Backup System
echo "<h3>Test 5: Backup System</h3>\n";
try {
    require_once 'includes/backup.php';
    $backup = new DatabaseBackup();
    
    $stats = $backup->getBackupStats();
    echo "<p>Backup directory: " . (is_dir(__DIR__ . '/backups') ? '✓ Exists' : '✗ Missing') . "</p>\n";
    echo "<p>Existing backups: {$stats['count']}</p>\n";
    echo "<p>Total backup size: {$stats['total_size_formatted']}</p>\n";
    
    echo "<p style='color: green;'>✓ Backup system accessible</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Backup system error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test 6: Migration System
echo "<h3>Test 6: Migration System</h3>\n";
try {
    require_once 'includes/migrate.php';
    $migration = new DatabaseMigration();
    
    $status = $migration->getStatus();
    $executed = array_filter($status, function($m) { return $m['executed']; });
    $pending = array_filter($status, function($m) { return !$m['executed']; });
    
    echo "<p>Total migrations: " . count($status) . "</p>\n";
    echo "<p>Executed: " . count($executed) . "</p>\n";
    echo "<p>Pending: " . count($pending) . "</p>\n";
    
    if (count($pending) === 0) {
        echo "<p style='color: green;'>✓ All migrations up to date</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ " . count($pending) . " pending migrations</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Migration system error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h3>Summary</h3>\n";
echo "<p>Database test completed. Check results above for any issues.</p>\n";
echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ul>\n";
echo "<li>If tables are missing, run: <code>php includes/install.php</code></li>\n";
echo "<li>If migrations are pending, run: <code>php includes/migrate.php migrate</code></li>\n";
echo "<li>Access web interface: <a href='database_manager.php'>Database Manager</a></li>\n";
echo "</ul>\n";
?>