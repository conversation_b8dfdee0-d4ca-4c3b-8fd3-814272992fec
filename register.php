<?php
/**
 * User Registration Page
 * Handles registration for customers and shop owners
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    $redirectUrl = $user['role'] === 'admin' ? '/dashboard/admin.php' : 
                  ($user['role'] === 'shop_owner' ? '/dashboard/shop-owner.php' : '/dashboard/customer.php');
    redirect($redirectUrl);
} 

$errors = [];
$success = false;
$formData = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        $errors['general'] = 'Security token mismatch. Please try again.';
    } else {
        // Get form data
        $formData = [
            'email' => $_POST['email'] ?? '',
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'first_name' => $_POST['first_name'] ?? '',
            'last_name' => $_POST['last_name'] ?? '',
            'phone' => $_POST['phone'] ?? '',
            'role' => $_POST['role'] ?? ''
        ];
        
        // Attempt registration
        $result = $auth->registerUser($formData);
        
        if ($result['success']) {
            $success = true;
            setFlashMessage('success', 'Registration successful! You can now log in with your credentials.');
            
            // Clear form data on success
            $formData = [];
        } else {
            $errors = $result['errors'];
        }
    }
}

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container">
        <div class="auth-wrapper">
            <div class="auth-card">
                <div class="auth-header">
                    <h1>Create Your Account</h1>
                    <p>Join our beauty platform community</p>
                </div>

                <?php if (!empty($flashMessages)): ?>
                    <div class="flash-messages">
                        <?php foreach ($flashMessages as $message): ?>
                            <div class="alert alert-<?php echo htmlspecialchars($message['type']); ?>">
                                <?php echo htmlspecialchars($message['message']); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="success-message">
                        <div class="alert alert-success">
                            <h3>Registration Successful!</h3>
                            <p>Your account has been created successfully. You can now log in with your credentials.</p>
                            <a href="login.php" class="btn btn-primary">Go to Login</a>
                        </div>
                    </div>
                <?php else: ?>
                    <?php if (isset($errors['general'])): ?>
                        <div class="alert alert-error">
                            <?php echo htmlspecialchars($errors['general']); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="register.php" class="auth-form" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
                        
                        <!-- Role Selection -->
                        <div class="form-group">
                            <label for="role">I want to register as:</label>
                            <div class="role-selection">
                                <div class="role-option">
                                    <input type="radio" id="role_customer" name="role" value="customer" 
                                           <?php echo (($formData['role'] ?? '') === 'customer') ? 'checked' : ''; ?> required>
                                    <label for="role_customer" class="role-label">
                                        <div class="role-icon">👤</div>
                                        <div class="role-info">
                                            <h3>Customer</h3>
                                            <p>Book appointments and discover beauty services</p>
                                        </div>
                                    </label>
                                </div>
                                
                                <div class="role-option">
                                    <input type="radio" id="role_shop_owner" name="role" value="shop_owner" 
                                           <?php echo (($formData['role'] ?? '') === 'shop_owner') ? 'checked' : ''; ?> required>
                                    <label for="role_shop_owner" class="role-label">
                                        <div class="role-icon">🏪</div>
                                        <div class="role-info">
                                            <h3>Shop Owner</h3>
                                            <p>Manage your beauty business and services</p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            <?php if (isset($errors['role'])): ?>
                                <div class="error-message"><?php echo htmlspecialchars($errors['role']); ?></div>
                            <?php endif; ?>
                        </div>

                        <!-- Personal Information -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="first_name">First Name *</label>
                                <input type="text" id="first_name" name="first_name" 
                                       value="<?php echo htmlspecialchars($formData['first_name'] ?? ''); ?>" 
                                       required maxlength="50">
                                <?php if (isset($errors['first_name'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['first_name']); ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="form-group">
                                <label for="last_name">Last Name *</label>
                                <input type="text" id="last_name" name="last_name" 
                                       value="<?php echo htmlspecialchars($formData['last_name'] ?? ''); ?>" 
                                       required maxlength="50">
                                <?php if (isset($errors['last_name'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['last_name']); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($formData['email'] ?? ''); ?>" 
                                   required maxlength="255">
                            <?php if (isset($errors['email'])): ?>
                                <div class="error-message"><?php echo htmlspecialchars($errors['email']); ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($formData['phone'] ?? ''); ?>" 
                                   maxlength="20" placeholder="Optional">
                            <?php if (isset($errors['phone'])): ?>
                                <div class="error-message"><?php echo htmlspecialchars($errors['phone']); ?></div>
                            <?php endif; ?>
                        </div>

                        <!-- Password Information -->
                        <div class="form-group">
                            <label for="password">Password *</label>
                            <input type="password" id="password" name="password" required>
                            <div class="password-requirements">
                                <small>Password must be at least 8 characters with uppercase, lowercase, and number</small>
                            </div>
                            <?php if (isset($errors['password'])): ?>
                                <div class="error-message"><?php echo htmlspecialchars($errors['password']); ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">Confirm Password *</label>
                            <input type="password" id="confirm_password" name="confirm_password" required>
                            <?php if (isset($errors['confirm_password'])): ?>
                                <div class="error-message"><?php echo htmlspecialchars($errors['confirm_password']); ?></div>
                            <?php endif; ?>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="terms" name="terms" required>
                                <label for="terms">
                                    I agree to the <a href="#" target="_blank">Terms of Service</a> and 
                                    <a href="#" target="_blank">Privacy Policy</a> *
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-full">Create Account</button>
                        </div>

                        <!-- Login Link -->
                        <div class="auth-links">
                            <p>Already have an account? <a href="login.php">Sign in here</a></p>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        // Client-side form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.auth-form');
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('confirm_password');
            
            // Real-time password validation
            passwordField.addEventListener('input', function() {
                const password = this.value;
                const requirements = document.querySelector('.password-requirements');
                
                // Check password strength
                const hasUppercase = /[A-Z]/.test(password);
                const hasLowercase = /[a-z]/.test(password);
                const hasNumber = /\d/.test(password);
                const hasMinLength = password.length >= 8;
                
                const isValid = hasUppercase && hasLowercase && hasNumber && hasMinLength;
                
                if (password.length > 0) {
                    if (isValid) {
                        this.classList.remove('invalid');
                        this.classList.add('valid');
                        requirements.style.color = '#38A169';
                    } else {
                        this.classList.remove('valid');
                        this.classList.add('invalid');
                        requirements.style.color = '#E53E3E';
                    }
                } else {
                    this.classList.remove('valid', 'invalid');
                    requirements.style.color = '';
                }
            });
            
            // Password confirmation validation
            confirmPasswordField.addEventListener('input', function() {
                const password = passwordField.value;
                const confirmPassword = this.value;
                
                if (confirmPassword.length > 0) {
                    if (password === confirmPassword) {
                        this.classList.remove('invalid');
                        this.classList.add('valid');
                    } else {
                        this.classList.remove('valid');
                        this.classList.add('invalid');
                    }
                } else {
                    this.classList.remove('valid', 'invalid');
                }
            });
            
            // Form submission validation
            form.addEventListener('submit', function(e) {
                let isValid = true;
                const formData = new FormData(form);
                
                // Check required fields
                const requiredFields = ['role', 'first_name', 'last_name', 'email', 'password', 'confirm_password'];
                requiredFields.forEach(field => {
                    const value = formData.get(field);
                    if (!value || value.trim() === '') {
                        isValid = false;
                        const fieldElement = document.getElementById(field) || document.querySelector(`input[name="${field}"]`);
                        if (fieldElement) {
                            fieldElement.classList.add('invalid');
                        }
                    }
                });
                
                // Check password match
                const password = formData.get('password');
                const confirmPassword = formData.get('confirm_password');
                if (password !== confirmPassword) {
                    isValid = false;
                    confirmPasswordField.classList.add('invalid');
                }
                
                // Check terms acceptance
                const termsCheckbox = document.getElementById('terms');
                if (!termsCheckbox.checked) {
                    isValid = false;
                    termsCheckbox.classList.add('invalid');
                }
                
                if (!isValid) {
                    e.preventDefault();
                    // Scroll to first error
                    const firstError = document.querySelector('.invalid');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstError.focus();
                    }
                }
            });
            
            // Remove invalid class on focus
            document.querySelectorAll('input').forEach(input => {
                input.addEventListener('focus', function() {
                    this.classList.remove('invalid');
                });
            });
        });
    </script>
</body>
</html>