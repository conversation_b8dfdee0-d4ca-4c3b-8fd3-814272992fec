<?php
/**
 * Shop Owner Dashboard
 * Complete dashboard for shop owners to manage their business, branches, and services
 */

require_once '../includes/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Require shop owner authentication
requireRole('shop_owner');

$user = getCurrentUser();
$db = new DatabaseQuery();

// Check if user has a registered shop
$shop = getShopByOwnerId($user['id']);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    // Verify CSRF token
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        echo json_encode(['success' => false, 'message' => 'Invalid security token']);
        exit;
    }
    
    switch ($_POST['action']) {
        case 'add_branch':
            echo json_encode(handleAddBranch($shop['id'], $_POST));
            exit;
            
        case 'edit_branch':
            echo json_encode(handleEditBranch($_POST));
            exit;
            
        case 'delete_branch':
            echo json_encode(handleDeleteBranch($_POST['branch_id']));
            exit;
            
        case 'add_service':
            echo json_encode(handleAddService($_POST));
            exit;
            
        case 'edit_service':
            echo json_encode(handleEditService($_POST));
            exit;
            
        case 'delete_service':
            echo json_encode(handleDeleteService($_POST['service_id']));
            exit;
            
        case 'add_staff':
            echo json_encode(handleAddStaff($_POST));
            exit;
            
        case 'edit_staff':
            echo json_encode(handleEditStaff($_POST));
            exit;
            
        case 'delete_staff':
            echo json_encode(handleDeleteStaff($_POST['staff_id']));
            exit;
            
        case 'confirm_appointment':
            echo json_encode(handleConfirmAppointment($_POST['appointment_id']));
            exit;
            
        case 'cancel_appointment':
            echo json_encode(handleCancelAppointment($_POST['appointment_id'], $_POST['reason'] ?? ''));
            exit;
            
        case 'reschedule_appointment':
            echo json_encode(handleRescheduleAppointment($_POST));
            exit;
            
        case 'get_appointments':
            echo json_encode(getAppointmentsByDate($_POST['date'], $_POST['view_type'] ?? 'day'));
            exit;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            exit;
    }
}

// Get shop branches if shop exists
$branches = [];
$services = [];
$staff = [];
if ($shop && $shop['status'] === 'approved') {
    $branches = $db->select('shop_branches', ['shop_id' => $shop['id']], [], 'created_at ASC');
    
    // Get services for all branches
    if (!empty($branches)) {
        $branchIds = array_column($branches, 'id');
        $placeholders = str_repeat('?,', count($branchIds) - 1) . '?';
        $sql = "SELECT s.*, sb.branch_name 
                FROM services s 
                JOIN shop_branches sb ON s.branch_id = sb.id 
                WHERE s.branch_id IN ($placeholders) 
                ORDER BY sb.branch_name, s.name";
        $services = $db->query($sql, $branchIds);
        
        // Get staff for all branches
        $sql = "SELECT st.*, sb.branch_name 
                FROM staff st 
                JOIN shop_branches sb ON st.branch_id = sb.id 
                WHERE st.branch_id IN ($placeholders) 
                ORDER BY sb.branch_name, st.name";
        $staff = $db->query($sql, $branchIds);
    }
}

// Get flash messages
$flashMessages = getFlashMessages();

// Ensure required functions exist (fallback if not loaded from functions.php)
if (!function_exists('branchHasActiveBookings')) {
    function branchHasActiveBookings($branchId) {
        try {
            $db = new DatabaseQuery();
            $sql = "
                SELECT COUNT(*) as count 
                FROM appointments a 
                JOIN services s ON a.service_id = s.id 
                WHERE s.branch_id = ? 
                AND a.status IN ('pending', 'confirmed') 
                AND a.appointment_date >= CURDATE()
            ";
            $result = $db->queryOne($sql, [$branchId]);
            return (int)$result['count'] > 0;
        } catch (Exception $e) {
            logError('Check branch bookings error: ' . $e->getMessage(), ['branch_id' => $branchId]);
            return true; // Err on the side of caution
        }
    }
}

if (!function_exists('serviceHasActiveBookings')) {
    function serviceHasActiveBookings($serviceId) {
        try {
            $db = new DatabaseQuery();
            $count = $db->count('appointments', [
                'service_id' => $serviceId,
                'status' => 'confirmed',
                'appointment_date >=' => date('Y-m-d')
            ]);
            return $count > 0;
        } catch (Exception $e) {
            logError('Check service bookings error: ' . $e->getMessage(), ['service_id' => $serviceId]);
            return true; // Err on the side of caution
        }
    }
}

if (!function_exists('getShopStatistics')) {
    function getShopStatistics($shopId) {
        try {
            $db = new DatabaseQuery();
            
            // Get total bookings
            $totalBookings = $db->queryOne("
                SELECT COUNT(*) as count 
                FROM appointments a 
                JOIN services s ON a.service_id = s.id 
                JOIN shop_branches sb ON s.branch_id = sb.id 
                WHERE sb.shop_id = ?
            ", [$shopId]);
            
            // Get total services
            $totalServices = $db->queryOne("
                SELECT COUNT(*) as count 
                FROM services s 
                JOIN shop_branches sb ON s.branch_id = sb.id 
                WHERE sb.shop_id = ?
            ", [$shopId]);
            
            // Get total branches
            $totalBranches = $db->count('shop_branches', ['shop_id' => $shopId]);
            
            // Get total reviews
            $totalReviews = $db->count('reviews', ['shop_id' => $shopId, 'status' => 'active']);
            
            return [
                'bookings' => (int)$totalBookings['count'],
                'services' => (int)$totalServices['count'],
                'branches' => $totalBranches,
                'reviews' => $totalReviews
            ];
        } catch (Exception $e) {
            logError('Get shop statistics error: ' . $e->getMessage(), ['shop_id' => $shopId]);
            return [
                'bookings' => 0,
                'services' => 0,
                'branches' => 0,
                'reviews' => 0
            ];
        }
    }
}

if (!function_exists('formatCurrency')) {
    function formatCurrency($amount, $currency = '$') {
        return $currency . number_format($amount, 2);
    }
}

/**
 * Handle adding a new branch
 */
function handleAddBranch($shopId, $data) {
    global $db;
    
    try {
        // Validate branch data
        $validationResult = validateBranchData($data);
        if (!$validationResult['valid']) {
            return ['success' => false, 'errors' => $validationResult['errors']];
        }
        
        // Sanitize and prepare data
        $branchData = [
            'shop_id' => $shopId,
            'branch_name' => sanitizeString($data['branch_name']),
            'address' => sanitizeText($data['address']),
            'city' => sanitizeString($data['city']),
            'country' => sanitizeString($data['country']),
            'phone' => !empty($data['phone']) ? sanitizePhone($data['phone']) : null,
            'email' => !empty($data['email']) ? sanitizeEmail($data['email']) : null,
            'working_hours' => json_encode($data['working_hours']),
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $branchId = $db->insert('shop_branches', $branchData);
        
        if ($branchId) {
            logSecurityEvent('branch_created', ['branch_id' => $branchId, 'shop_id' => $shopId]);
            return ['success' => true, 'message' => 'Branch added successfully', 'branch_id' => $branchId];
        } else {
            return ['success' => false, 'message' => 'Failed to add branch'];
        }
        
    } catch (Exception $e) {
        logError('Add branch error: ' . $e->getMessage(), $data);
        return ['success' => false, 'message' => 'Failed to add branch'];
    }
}

/**
 * Handle editing a branch
 */
function handleEditBranch($data) {
    global $db, $shop;
    
    try {
        $branchId = (int)$data['branch_id'];
        
        // Verify branch belongs to current shop
        $branch = $db->selectOne('shop_branches', ['id' => $branchId, 'shop_id' => $shop['id']]);
        if (!$branch) {
            return ['success' => false, 'message' => 'Branch not found'];
        }
        
        // Validate branch data
        $validationResult = validateBranchData($data);
        if (!$validationResult['valid']) {
            return ['success' => false, 'errors' => $validationResult['errors']];
        }
        
        // Sanitize and prepare data
        $updateData = [
            'branch_name' => sanitizeString($data['branch_name']),
            'address' => sanitizeText($data['address']),
            'city' => sanitizeString($data['city']),
            'country' => sanitizeString($data['country']),
            'phone' => !empty($data['phone']) ? sanitizePhone($data['phone']) : null,
            'email' => !empty($data['email']) ? sanitizeEmail($data['email']) : null,
            'working_hours' => json_encode($data['working_hours']),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $result = $db->update('shop_branches', $updateData, ['id' => $branchId]);
        
        if ($result) {
            logSecurityEvent('branch_updated', ['branch_id' => $branchId, 'shop_id' => $shop['id']]);
            return ['success' => true, 'message' => 'Branch updated successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to update branch'];
        }
        
    } catch (Exception $e) {
        logError('Edit branch error: ' . $e->getMessage(), $data);
        return ['success' => false, 'message' => 'Failed to update branch'];
    }
}

/**
 * Handle deleting a branch
 */
function handleDeleteBranch($branchId) {
    global $db, $shop;
    
    try {
        $branchId = (int)$branchId;
        
        // Verify branch belongs to current shop
        $branch = $db->selectOne('shop_branches', ['id' => $branchId, 'shop_id' => $shop['id']]);
        if (!$branch) {
            return ['success' => false, 'message' => 'Branch not found'];
        }
        
        // Check if branch has active bookings
        $activeBookings = branchHasActiveBookings($branchId);
        
        if ($activeBookings) {
            return ['success' => false, 'message' => 'Cannot delete branch with active bookings'];
        }
        
        // Delete branch (services will be deleted by CASCADE)
        $result = $db->delete('shop_branches', ['id' => $branchId]);
        
        if ($result) {
            logSecurityEvent('branch_deleted', ['branch_id' => $branchId, 'shop_id' => $shop['id']]);
            return ['success' => true, 'message' => 'Branch deleted successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to delete branch'];
        }
        
    } catch (Exception $e) {
        logError('Delete branch error: ' . $e->getMessage(), ['branch_id' => $branchId]);
        return ['success' => false, 'message' => 'Failed to delete branch'];
    }
}

/**
 * Handle adding a new service
 */
function handleAddService($data) {
    global $db, $shop;
    
    try {
        $branchId = (int)$data['branch_id'];
        
        // Verify branch belongs to current shop
        $branch = $db->selectOne('shop_branches', ['id' => $branchId, 'shop_id' => $shop['id']]);
        if (!$branch) {
            return ['success' => false, 'message' => 'Branch not found'];
        }
        
        // Validate service data
        $validationResult = validateServiceData($data);
        if (!$validationResult['valid']) {
            return ['success' => false, 'errors' => $validationResult['errors']];
        }
        
        // Sanitize and prepare data
        $serviceData = [
            'branch_id' => $branchId,
            'name' => sanitizeString($data['name']),
            'description' => !empty($data['description']) ? sanitizeText($data['description']) : null,
            'duration_minutes' => (int)$data['duration_minutes'],
            'price' => (float)$data['price'],
            'category' => !empty($data['category']) ? sanitizeString($data['category']) : null,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $serviceId = $db->insert('services', $serviceData);
        
        if ($serviceId) {
            logSecurityEvent('service_created', ['service_id' => $serviceId, 'branch_id' => $branchId]);
            return ['success' => true, 'message' => 'Service added successfully', 'service_id' => $serviceId];
        } else {
            return ['success' => false, 'message' => 'Failed to add service'];
        }
        
    } catch (Exception $e) {
        logError('Add service error: ' . $e->getMessage(), $data);
        return ['success' => false, 'message' => 'Failed to add service'];
    }
}

/**
 * Handle editing a service
 */
function handleEditService($data) {
    global $db, $shop;
    
    try {
        $serviceId = (int)$data['service_id'];
        
        // Verify service belongs to current shop
        $service = $db->queryOne("
            SELECT s.* FROM services s 
            JOIN shop_branches sb ON s.branch_id = sb.id 
            WHERE s.id = ? AND sb.shop_id = ?
        ", [$serviceId, $shop['id']]);
        
        if (!$service) {
            return ['success' => false, 'message' => 'Service not found'];
        }
        
        // Validate service data
        $validationResult = validateServiceData($data);
        if (!$validationResult['valid']) {
            return ['success' => false, 'errors' => $validationResult['errors']];
        }
        
        // Sanitize and prepare data
        $updateData = [
            'name' => sanitizeString($data['name']),
            'description' => !empty($data['description']) ? sanitizeText($data['description']) : null,
            'duration_minutes' => (int)$data['duration_minutes'],
            'price' => (float)$data['price'],
            'category' => !empty($data['category']) ? sanitizeString($data['category']) : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $result = $db->update('services', $updateData, ['id' => $serviceId]);
        
        if ($result) {
            logSecurityEvent('service_updated', ['service_id' => $serviceId]);
            return ['success' => true, 'message' => 'Service updated successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to update service'];
        }
        
    } catch (Exception $e) {
        logError('Edit service error: ' . $e->getMessage(), $data);
        return ['success' => false, 'message' => 'Failed to update service'];
    }
}

/**
 * Handle deleting a service
 */
function handleDeleteService($serviceId) {
    global $db, $shop;
    
    try {
        $serviceId = (int)$serviceId;
        
        // Verify service belongs to current shop
        $service = $db->queryOne("
            SELECT s.* FROM services s 
            JOIN shop_branches sb ON s.branch_id = sb.id 
            WHERE s.id = ? AND sb.shop_id = ?
        ", [$serviceId, $shop['id']]);
        
        if (!$service) {
            return ['success' => false, 'message' => 'Service not found'];
        }
        
        // Check if service has active bookings
        $activeBookings = serviceHasActiveBookings($serviceId);
        
        if ($activeBookings) {
            return ['success' => false, 'message' => 'Cannot delete service with active bookings'];
        }
        
        // Delete service
        $result = $db->delete('services', ['id' => $serviceId]);
        
        if ($result) {
            logSecurityEvent('service_deleted', ['service_id' => $serviceId]);
            return ['success' => true, 'message' => 'Service deleted successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to delete service'];
        }
        
    } catch (Exception $e) {
        logError('Delete service error: ' . $e->getMessage(), ['service_id' => $serviceId]);
        return ['success' => false, 'message' => 'Failed to delete service'];
    }
}

/**
 * Validate branch data
 */
function validateBranchData($data) {
    $errors = [];
    
    if (empty($data['branch_name'])) {
        $errors['branch_name'] = 'Branch name is required';
    } elseif (strlen($data['branch_name']) > 255) {
        $errors['branch_name'] = 'Branch name must be less than 255 characters';
    }
    
    if (empty($data['address'])) {
        $errors['address'] = 'Address is required';
    }
    
    if (empty($data['city'])) {
        $errors['city'] = 'City is required';
    }
    
    if (empty($data['country'])) {
        $errors['country'] = 'Country is required';
    }
    
    if (!empty($data['phone']) && !validatePhone($data['phone'])) {
        $errors['phone'] = 'Please enter a valid phone number';
    }
    
    if (!empty($data['email']) && !validateEmail($data['email'])) {
        $errors['email'] = 'Please enter a valid email address';
    }
    
    if (empty($data['working_hours']) || !is_array($data['working_hours'])) {
        $errors['working_hours'] = 'Working hours are required';
    } else {
        $workingHoursErrors = validateWorkingHours($data['working_hours']);
        if (!empty($workingHoursErrors)) {
            $errors = array_merge($errors, $workingHoursErrors);
        }
    }
    
    return ['valid' => empty($errors), 'errors' => $errors];
}

/**
 * Validate service data
 */
function validateServiceData($data) {
    $errors = [];
    
    if (empty($data['name'])) {
        $errors['name'] = 'Service name is required';
    } elseif (strlen($data['name']) > 255) {
        $errors['name'] = 'Service name must be less than 255 characters';
    }
    
    if (empty($data['duration_minutes']) || !is_numeric($data['duration_minutes']) || $data['duration_minutes'] <= 0) {
        $errors['duration_minutes'] = 'Duration must be a positive number';
    }
    
    if (empty($data['price']) || !validatePrice($data['price'])) {
        $errors['price'] = 'Please enter a valid price';
    }
    
    if (!empty($data['description']) && strlen($data['description']) > 1000) {
        $errors['description'] = 'Description must be less than 1000 characters';
    }
    
    return ['valid' => empty($errors), 'errors' => $errors];
}

/**
 * Handle adding a new staff member
 */
function handleAddStaff($data) {
    global $db, $shop;
    
    try {
        $branchId = (int)$data['branch_id'];
        
        // Verify branch belongs to current shop
        $branch = $db->selectOne('shop_branches', ['id' => $branchId, 'shop_id' => $shop['id']]);
        if (!$branch) {
            return ['success' => false, 'message' => 'Branch not found'];
        }
        
        // Validate staff data
        $validationResult = validateStaffData($data);
        if (!$validationResult['valid']) {
            return ['success' => false, 'errors' => $validationResult['errors']];
        }
        
        // Sanitize and prepare data
        $staffData = [
            'branch_id' => $branchId,
            'name' => sanitizeString($data['name']),
            'email' => !empty($data['email']) ? sanitizeEmail($data['email']) : null,
            'phone' => !empty($data['phone']) ? sanitizePhone($data['phone']) : null,
            'specialties' => !empty($data['specialties']) ? json_encode($data['specialties']) : null,
            'working_hours' => json_encode($data['working_hours']),
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $staffId = $db->insert('staff', $staffData);
        
        if ($staffId) {
            logSecurityEvent('staff_created', ['staff_id' => $staffId, 'branch_id' => $branchId]);
            return ['success' => true, 'message' => 'Staff member added successfully', 'staff_id' => $staffId];
        } else {
            return ['success' => false, 'message' => 'Failed to add staff member'];
        }
        
    } catch (Exception $e) {
        logError('Add staff error: ' . $e->getMessage(), $data);
        return ['success' => false, 'message' => 'Failed to add staff member'];
    }
}

/**
 * Handle editing a staff member
 */
function handleEditStaff($data) {
    global $db, $shop;
    
    try {
        $staffId = (int)$data['staff_id'];
        
        // Verify staff belongs to current shop
        $staff = $db->queryOne("
            SELECT st.* FROM staff st 
            JOIN shop_branches sb ON st.branch_id = sb.id 
            WHERE st.id = ? AND sb.shop_id = ?
        ", [$staffId, $shop['id']]);
        
        if (!$staff) {
            return ['success' => false, 'message' => 'Staff member not found'];
        }
        
        // Validate staff data
        $validationResult = validateStaffData($data);
        if (!$validationResult['valid']) {
            return ['success' => false, 'errors' => $validationResult['errors']];
        }
        
        // Sanitize and prepare data
        $updateData = [
            'name' => sanitizeString($data['name']),
            'email' => !empty($data['email']) ? sanitizeEmail($data['email']) : null,
            'phone' => !empty($data['phone']) ? sanitizePhone($data['phone']) : null,
            'specialties' => !empty($data['specialties']) ? json_encode($data['specialties']) : null,
            'working_hours' => json_encode($data['working_hours']),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $result = $db->update('staff', $updateData, ['id' => $staffId]);
        
        if ($result) {
            logSecurityEvent('staff_updated', ['staff_id' => $staffId]);
            return ['success' => true, 'message' => 'Staff member updated successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to update staff member'];
        }
        
    } catch (Exception $e) {
        logError('Edit staff error: ' . $e->getMessage(), $data);
        return ['success' => false, 'message' => 'Failed to update staff member'];
    }
}

/**
 * Handle deleting a staff member
 */
function handleDeleteStaff($staffId) {
    global $db, $shop;
    
    try {
        $staffId = (int)$staffId;
        
        // Verify staff belongs to current shop
        $staff = $db->queryOne("
            SELECT st.* FROM staff st 
            JOIN shop_branches sb ON st.branch_id = sb.id 
            WHERE st.id = ? AND sb.shop_id = ?
        ", [$staffId, $shop['id']]);
        
        if (!$staff) {
            return ['success' => false, 'message' => 'Staff member not found'];
        }
        
        // Check if staff has active appointments
        $activeAppointments = staffHasActiveAppointments($staffId);
        
        if ($activeAppointments) {
            return ['success' => false, 'message' => 'Cannot delete staff member with active appointments'];
        }
        
        // Delete staff member
        $result = $db->delete('staff', ['id' => $staffId]);
        
        if ($result) {
            logSecurityEvent('staff_deleted', ['staff_id' => $staffId]);
            return ['success' => true, 'message' => 'Staff member deleted successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to delete staff member'];
        }
        
    } catch (Exception $e) {
        logError('Delete staff error: ' . $e->getMessage(), ['staff_id' => $staffId]);
        return ['success' => false, 'message' => 'Failed to delete staff member'];
    }
}

/**
 * Validate staff data
 */
function validateStaffData($data) {
    $errors = [];
    
    if (empty($data['name'])) {
        $errors['name'] = 'Staff name is required';
    } elseif (strlen($data['name']) > 255) {
        $errors['name'] = 'Staff name must be less than 255 characters';
    }
    
    if (!empty($data['email']) && !validateEmail($data['email'])) {
        $errors['email'] = 'Please enter a valid email address';
    }
    
    if (!empty($data['phone']) && !validatePhone($data['phone'])) {
        $errors['phone'] = 'Please enter a valid phone number';
    }
    
    if (empty($data['working_hours']) || !is_array($data['working_hours'])) {
        $errors['working_hours'] = 'Working hours are required';
    } else {
        $workingHoursErrors = validateWorkingHours($data['working_hours']);
        if (!empty($workingHoursErrors)) {
            $errors = array_merge($errors, $workingHoursErrors);
        }
    }
    
    return ['valid' => empty($errors), 'errors' => $errors];
}

/**
 * Check if staff member has active appointments
 */
function staffHasActiveAppointments($staffId) {
    try {
        $db = new DatabaseQuery();
        $count = $db->count('appointments', [
            'staff_id' => $staffId,
            'status' => ['pending', 'confirmed'],
            'appointment_date >=' => date('Y-m-d')
        ]);
        return $count > 0;
    } catch (Exception $e) {
        logError('Check staff appointments error: ' . $e->getMessage(), ['staff_id' => $staffId]);
        return true; // Err on the side of caution
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop Owner Dashboard - Beauty Platform</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="shop-owner-dashboard">
    <div class="dashboard-container">
        <!-- Shop Owner Navigation -->
        <nav class="dashboard-nav">
            <div class="nav-header">
                <h2>Shop Owner Panel</h2>
                <p>Welcome, <?php echo htmlspecialchars($user['name']); ?></p>
            </div>
            
            <ul class="nav-menu">
                <li><a href="#overview" class="nav-link active">Overview</a></li>
                <?php if ($shop): ?>
                    <li><a href="#shop-details" class="nav-link">Shop Details</a></li>
                    <li><a href="#branches" class="nav-link">Branches</a></li>
                    <li><a href="#services" class="nav-link">Services</a></li>
                    <li><a href="#staff" class="nav-link">Staff</a></li>
                    <li><a href="#appointments" class="nav-link">Appointments</a></li>
                <?php endif; ?>
            </ul>
            
            <div class="nav-footer">
                <a href="../" class="btn btn-secondary">Back to Site</a>
                <a href="../logout.php" class="btn btn-outline">Logout</a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Flash Messages -->
            <?php if (!empty($flashMessages)): ?>
                <div class="flash-messages">
                    <?php foreach ($flashMessages as $message): ?>
                        <div class="alert alert-<?php echo htmlspecialchars($message['type']); ?>">
                            <?php echo htmlspecialchars($message['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <!-- Overview Section -->
            <section id="overview" class="dashboard-section active">
                <div class="section-header">
                    <h2>Dashboard Overview</h2>
                    <p>Manage your beauty business on our platform</p>
                </div>

                <?php if (!$shop): ?>
                    <!-- No Shop Registered -->
                    <div class="welcome-card">
                        <div class="welcome-content">
                            <h3>Welcome to Beauty Platform!</h3>
                            <p>You haven't registered your business yet. Get started by registering your beauty shop to connect with customers.</p>
                            
                            <div class="welcome-steps">
                                <div class="step">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h4>Register Your Business</h4>
                                        <p>Provide your business details and main branch information</p>
                                    </div>
                                </div>
                                
                                <div class="step">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h4>Wait for Approval</h4>
                                        <p>Our admin team will review your registration</p>
                                    </div>
                                </div>
                                
                                <div class="step">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h4>Start Managing</h4>
                                        <p>Once approved, manage your services and bookings</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="welcome-actions">
                                <a href="../shop-register.php" class="btn btn-primary">Register Your Business</a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Shop Status Display -->
                    <div class="shop-status-card">
                        <div class="shop-status-header">
                            <h3><?php echo htmlspecialchars($shop['business_name']); ?></h3>
                            <span class="status-badge <?php echo $shop['status']; ?>">
                                <?php echo ucfirst($shop['status']); ?>
                            </span>
                        </div>
                        
                        <div class="shop-status-content">
                            <?php if ($shop['status'] === 'pending'): ?>
                                <div class="status-message pending">
                                    <h4>Registration Under Review</h4>
                                    <p>Your business registration is currently being reviewed by our admin team. You'll receive a notification once it's approved.</p>
                                    <p><strong>Submitted:</strong> <?php echo formatDate($shop['created_at']); ?></p>
                                </div>
                            <?php elseif ($shop['status'] === 'approved'): ?>
                                <div class="status-message approved">
                                    <h4>Business Approved!</h4>
                                    <p>Congratulations! Your business has been approved and is now live on our platform.</p>
                                    <p><strong>Approved:</strong> <?php echo formatDate($shop['updated_at']); ?></p>
                                </div>
                                
                                <!-- Quick Stats -->
                                <?php $stats = getShopStatistics($shop['id']); ?>
                                <div class="quick-stats">
                                    <div class="stat-item">
                                        <h4><?php echo $stats['bookings']; ?></h4>
                                        <p>Total Bookings</p>
                                    </div>
                                    <div class="stat-item">
                                        <h4><?php echo $stats['services']; ?></h4>
                                        <p>Services</p>
                                    </div>
                                    <div class="stat-item">
                                        <h4><?php echo $stats['branches']; ?></h4>
                                        <p>Branches</p>
                                    </div>
                                    <div class="stat-item">
                                        <h4><?php echo $stats['reviews']; ?></h4>
                                        <p>Reviews</p>
                                    </div>
                                </div>
                            <?php elseif ($shop['status'] === 'rejected'): ?>
                                <div class="status-message rejected">
                                    <h4>Registration Rejected</h4>
                                    <p>Unfortunately, your business registration was not approved.</p>
                                    <?php if (!empty($shop['rejection_reason'])): ?>
                                        <p><strong>Reason:</strong> <?php echo htmlspecialchars($shop['rejection_reason']); ?></p>
                                    <?php endif; ?>
                                    <p>Please contact support if you have questions or would like to resubmit.</p>
                                </div>
                            <?php elseif ($shop['status'] === 'suspended'): ?>
                                <div class="status-message suspended">
                                    <h4>Account Suspended</h4>
                                    <p>Your business account has been temporarily suspended. Please contact support for more information.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if ($shop['status'] === 'approved'): ?>
                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <h3>Quick Actions</h3>
                            <div class="actions-grid">
                                <a href="#services" class="action-card">
                                    <div class="action-icon">💄</div>
                                    <h4>Manage Services</h4>
                                    <p>Add, edit, or remove your beauty services</p>
                                </a>
                                
                                <a href="#branches" class="action-card">
                                    <div class="action-icon">🏪</div>
                                    <h4>Manage Branches</h4>
                                    <p>Update branch information and hours</p>
                                </a>
                                
                                <a href="#appointments" class="action-card">
                                    <div class="action-icon">📅</div>
                                    <h4>View Appointments</h4>
                                    <p>Check upcoming bookings and schedule</p>
                                </a>
                                
                                <a href="#reviews" class="action-card">
                                    <div class="action-icon">⭐</div>
                                    <h4>Customer Reviews</h4>
                                    <p>View and respond to customer feedback</p>
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </section>

            <!-- Shop Details Section -->
            <?php if ($shop): ?>
                <section id="shop-details" class="dashboard-section">
                    <div class="section-header">
                        <h2>Shop Details</h2>
                        <p>Your business information</p>
                    </div>
                    
                    <div class="shop-details-card">
                        <div class="detail-row">
                            <strong>Business Name:</strong>
                            <span><?php echo htmlspecialchars($shop['business_name']); ?></span>
                        </div>
                        
                        <?php if (!empty($shop['description'])): ?>
                            <div class="detail-row">
                                <strong>Description:</strong>
                                <span><?php echo htmlspecialchars($shop['description']); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($shop['business_license'])): ?>
                            <div class="detail-row">
                                <strong>Business License:</strong>
                                <span><?php echo htmlspecialchars($shop['business_license']); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="detail-row">
                            <strong>Status:</strong>
                            <span class="status-badge <?php echo $shop['status']; ?>">
                                <?php echo ucfirst($shop['status']); ?>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <strong>Registered:</strong>
                            <span><?php echo formatDate($shop['created_at']); ?></span>
                        </div>
                    </div>
                </section>
                
                <!-- Branches Section -->
                <section id="branches" class="dashboard-section">
                    <div class="section-header">
                        <h2>Branch Locations</h2>
                        <p>Manage your business locations</p>
                        <?php if ($shop && $shop['status'] === 'approved'): ?>
                            <button class="btn btn-primary" onclick="showAddBranchModal()">Add New Branch</button>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($shop && $shop['status'] === 'approved'): ?>
                        <?php if (empty($branches)): ?>
                            <div class="empty-state">
                                <h3>No Branches Yet</h3>
                                <p>Add your first branch location to start managing your business.</p>
                                <button class="btn btn-primary" onclick="showAddBranchModal()">Add First Branch</button>
                            </div>
                        <?php else: ?>
                            <div class="branches-grid">
                                <?php foreach ($branches as $branch): ?>
                                    <div class="branch-card" data-branch-id="<?php echo $branch['id']; ?>">
                                        <div class="branch-header">
                                            <h3><?php echo htmlspecialchars($branch['branch_name']); ?></h3>
                                            <div class="branch-actions">
                                                <button class="btn btn-sm btn-outline" onclick="editBranch(<?php echo $branch['id']; ?>)">Edit</button>
                                                <button class="btn btn-sm btn-danger" onclick="deleteBranch(<?php echo $branch['id']; ?>)">Delete</button>
                                            </div>
                                        </div>
                                        
                                        <div class="branch-details">
                                            <p><strong>Address:</strong> <?php echo htmlspecialchars($branch['address']); ?></p>
                                            <p><strong>Location:</strong> <?php echo htmlspecialchars($branch['city'] . ', ' . $branch['country']); ?></p>
                                            
                                            <?php if (!empty($branch['phone'])): ?>
                                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($branch['phone']); ?></p>
                                            <?php endif; ?>
                                            
                                            <?php if (!empty($branch['email'])): ?>
                                                <p><strong>Email:</strong> <?php echo htmlspecialchars($branch['email']); ?></p>
                                            <?php endif; ?>
                                            
                                            <div class="working-hours">
                                                <strong>Working Hours:</strong>
                                                <?php 
                                                $workingHours = parseWorkingHours($branch['working_hours']);
                                                $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                                                ?>
                                                <div class="hours-grid">
                                                    <?php foreach ($days as $day): ?>
                                                        <div class="day-hours">
                                                            <span class="day"><?php echo ucfirst($day); ?>:</span>
                                                            <span class="hours">
                                                                <?php 
                                                                if (isset($workingHours[$day]) && !($workingHours[$day]['closed'] ?? false)) {
                                                                    echo formatTime($workingHours[$day]['open']) . ' - ' . formatTime($workingHours[$day]['close']);
                                                                } else {
                                                                    echo 'Closed';
                                                                }
                                                                ?>
                                                            </span>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <h3>Branch Management</h3>
                            <p>Branch management will be available once your shop is approved.</p>
                        </div>
                    <?php endif; ?>
                </section>
                
                <!-- Services Section -->
                <section id="services" class="dashboard-section">
                    <div class="section-header">
                        <h2>Services</h2>
                        <p>Manage your beauty services and pricing</p>
                        <?php if ($shop && $shop['status'] === 'approved' && !empty($branches)): ?>
                            <button class="btn btn-primary" onclick="showAddServiceModal()">Add New Service</button>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($shop && $shop['status'] === 'approved'): ?>
                        <?php if (empty($branches)): ?>
                            <div class="empty-state">
                                <h3>Add Branches First</h3>
                                <p>You need to add at least one branch before you can create services.</p>
                                <a href="#branches" class="btn btn-primary nav-link">Manage Branches</a>
                            </div>
                        <?php elseif (empty($services)): ?>
                            <div class="empty-state">
                                <h3>No Services Yet</h3>
                                <p>Add your first service to start accepting bookings.</p>
                                <button class="btn btn-primary" onclick="showAddServiceModal()">Add First Service</button>
                            </div>
                        <?php else: ?>
                            <div class="services-grid">
                                <?php 
                                $currentBranch = '';
                                foreach ($services as $service): 
                                    if ($currentBranch !== $service['branch_name']):
                                        if ($currentBranch !== '') echo '</div>';
                                        $currentBranch = $service['branch_name'];
                                ?>
                                        <div class="branch-services">
                                            <h3 class="branch-title"><?php echo htmlspecialchars($currentBranch); ?></h3>
                                            <div class="services-list">
                                <?php endif; ?>
                                
                                <div class="service-card" data-service-id="<?php echo $service['id']; ?>">
                                    <div class="service-header">
                                        <h4><?php echo htmlspecialchars($service['name']); ?></h4>
                                        <div class="service-actions">
                                            <button class="btn btn-sm btn-outline" onclick="editService(<?php echo $service['id']; ?>)">Edit</button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteService(<?php echo $service['id']; ?>)">Delete</button>
                                        </div>
                                    </div>
                                    
                                    <div class="service-details">
                                        <?php if (!empty($service['description'])): ?>
                                            <p class="service-description"><?php echo htmlspecialchars($service['description']); ?></p>
                                        <?php endif; ?>
                                        
                                        <div class="service-meta">
                                            <span class="service-duration">⏱️ <?php echo $service['duration_minutes']; ?> min</span>
                                            <span class="service-price">💰 <?php echo formatCurrency($service['price']); ?></span>
                                            <?php if (!empty($service['category'])): ?>
                                                <span class="service-category">🏷️ <?php echo htmlspecialchars($service['category']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php endforeach; ?>
                                <?php if (!empty($services)): ?>
                                            </div>
                                        </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <h3>Service Management</h3>
                            <p>Service management will be available once your shop is approved.</p>
                        </div>
                    <?php endif; ?>
                </section>
                
                <!-- Staff Section -->
                <section id="staff" class="dashboard-section">
                    <div class="section-header">
                        <h2>Staff Management</h2>
                        <p>Manage your team members and their schedules</p>
                        <?php if ($shop && $shop['status'] === 'approved' && !empty($branches)): ?>
                            <button class="btn btn-primary" onclick="showAddStaffModal()">Add New Staff Member</button>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($shop && $shop['status'] === 'approved'): ?>
                        <?php if (empty($branches)): ?>
                            <div class="empty-state">
                                <h3>Add Branches First</h3>
                                <p>You need to add at least one branch before you can add staff members.</p>
                                <a href="#branches" class="btn btn-primary nav-link">Manage Branches</a>
                            </div>
                        <?php elseif (empty($staff)): ?>
                            <div class="empty-state">
                                <h3>No Staff Members Yet</h3>
                                <p>Add your first staff member to start managing schedules and appointments.</p>
                                <button class="btn btn-primary" onclick="showAddStaffModal()">Add First Staff Member</button>
                            </div>
                        <?php else: ?>
                            <div class="staff-grid">
                                <?php 
                                $currentBranch = '';
                                foreach ($staff as $staffMember): 
                                    if ($currentBranch !== $staffMember['branch_name']):
                                        if ($currentBranch !== '') echo '</div>';
                                        $currentBranch = $staffMember['branch_name'];
                                ?>
                                        <div class="branch-staff">
                                            <h3 class="branch-title"><?php echo htmlspecialchars($currentBranch); ?></h3>
                                            <div class="staff-list">
                                <?php endif; ?>
                                
                                <div class="staff-card" data-staff-id="<?php echo $staffMember['id']; ?>">
                                    <div class="staff-header">
                                        <h4><?php echo htmlspecialchars($staffMember['name']); ?></h4>
                                        <div class="staff-actions">
                                            <button class="btn btn-sm btn-outline" onclick="editStaff(<?php echo $staffMember['id']; ?>)">Edit</button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteStaff(<?php echo $staffMember['id']; ?>)">Delete</button>
                                        </div>
                                    </div>
                                    
                                    <div class="staff-details">
                                        <?php if (!empty($staffMember['email'])): ?>
                                            <p><strong>Email:</strong> <?php echo htmlspecialchars($staffMember['email']); ?></p>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($staffMember['phone'])): ?>
                                            <p><strong>Phone:</strong> <?php echo htmlspecialchars($staffMember['phone']); ?></p>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($staffMember['specialties'])): ?>
                                            <div class="staff-specialties">
                                                <strong>Specialties:</strong>
                                                <?php 
                                                $specialties = json_decode($staffMember['specialties'], true);
                                                if (is_array($specialties) && !empty($specialties)):
                                                ?>
                                                    <div class="specialties-tags">
                                                        <?php foreach ($specialties as $specialty): ?>
                                                            <span class="specialty-tag"><?php echo htmlspecialchars($specialty); ?></span>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="working-hours">
                                            <strong>Working Hours:</strong>
                                            <?php 
                                            $workingHours = parseWorkingHours($staffMember['working_hours']);
                                            $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                                            ?>
                                            <div class="hours-grid">
                                                <?php foreach ($days as $day): ?>
                                                    <div class="day-hours">
                                                        <span class="day"><?php echo ucfirst($day); ?>:</span>
                                                        <span class="hours">
                                                            <?php 
                                                            if (isset($workingHours[$day]) && !($workingHours[$day]['closed'] ?? false)) {
                                                                echo formatTime($workingHours[$day]['open']) . ' - ' . formatTime($workingHours[$day]['close']);
                                                            } else {
                                                                echo 'Off';
                                                            }
                                                            ?>
                                                        </span>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php endforeach; ?>
                                <?php if (!empty($staff)): ?>
                                            </div>
                                        </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <h3>Staff Management</h3>
                            <p>Staff management will be available once your shop is approved.</p>
                        </div>
                    <?php endif; ?>
                </section>

                <!-- Appointments Section -->
                <section id="appointments" class="dashboard-section">
                    <div class="section-header">
                        <h2>Appointment Management</h2>
                        <p>View and manage your appointments</p>
                    </div>
                    
                    <?php if ($shop && $shop['status'] === 'approved'): ?>
                        <div class="appointments-controls">
                            <div class="view-controls">
                                <button class="btn btn-sm view-btn active" data-view="day">Day</button>
                                <button class="btn btn-sm view-btn" data-view="week">Week</button>
                                <button class="btn btn-sm view-btn" data-view="month">Month</button>
                            </div>
                            
                            <div class="date-controls">
                                <button class="btn btn-sm btn-outline" onclick="navigateDate(-1)">&lt; Previous</button>
                                <input type="date" id="appointmentDate" value="<?php echo date('Y-m-d'); ?>" onchange="loadAppointments()">
                                <button class="btn btn-sm btn-outline" onclick="navigateDate(1)">Next &gt;</button>
                                <button class="btn btn-sm btn-secondary" onclick="goToToday()">Today</button>
                            </div>
                        </div>
                        
                        <div id="appointmentsContainer" class="appointments-container">
                            <div class="loading-spinner" id="appointmentsLoading">Loading appointments...</div>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <h3>Appointment Management</h3>
                            <p>Appointment management will be available once your shop is approved.</p>
                        </div>
                    <?php endif; ?>
                </section>
            <?php endif; ?>
        </main>
    </div>

    <!-- Appointment Action Modal -->
    <div id="appointmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="appointmentModalTitle">Appointment Actions</h3>
                <span class="close" onclick="closeAppointmentModal()">&times;</span>
            </div>
            
            <div class="modal-body">
                <div id="appointmentDetails" class="appointment-details"></div>
                
                <div class="appointment-actions">
                    <button id="confirmBtn" class="btn btn-success" onclick="confirmAppointment()">Confirm</button>
                    <button id="rescheduleBtn" class="btn btn-primary" onclick="showRescheduleForm()">Reschedule</button>
                    <button id="cancelBtn" class="btn btn-danger" onclick="showCancelForm()">Cancel</button>
                </div>
                
                <div id="rescheduleForm" class="action-form" style="display: none;">
                    <h4>Reschedule Appointment</h4>
                    <div class="form-group">
                        <label for="newDate">New Date:</label>
                        <input type="date" id="newDate" name="new_date" required>
                    </div>
                    <div class="form-group">
                        <label for="newTime">New Time:</label>
                        <input type="time" id="newTime" name="new_time" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-primary" onclick="submitReschedule()">Reschedule</button>
                        <button type="button" class="btn btn-secondary" onclick="hideActionForms()">Cancel</button>
                    </div>
                </div>
                
                <div id="cancelForm" class="action-form" style="display: none;">
                    <h4>Cancel Appointment</h4>
                    <div class="form-group">
                        <label for="cancelReason">Reason for cancellation:</label>
                        <textarea id="cancelReason" name="reason" rows="3" placeholder="Optional reason for cancellation"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-danger" onclick="submitCancel()">Cancel Appointment</button>
                        <button type="button" class="btn btn-secondary" onclick="hideActionForms()">Back</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Branch Modal -->
    <div id="branchModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="branchModalTitle">Add New Branch</h3>
                <span class="close" onclick="closeBranchModal()">&times;</span>
            </div>
            
            <form id="branchForm" onsubmit="submitBranchForm(event)">
                <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
                <input type="hidden" name="action" value="add_branch">
                <input type="hidden" name="branch_id" id="branchId">
                
                <div class="form-group">
                    <label for="branchName">Branch Name *</label>
                    <input type="text" id="branchName" name="branch_name" required>
                    <div class="error-message" id="branchNameError"></div>
                </div>
                
                <div class="form-group">
                    <label for="branchAddress">Address *</label>
                    <textarea id="branchAddress" name="address" required rows="3"></textarea>
                    <div class="error-message" id="addressError"></div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="branchCity">City *</label>
                        <input type="text" id="branchCity" name="city" required>
                        <div class="error-message" id="cityError"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="branchCountry">Country *</label>
                        <input type="text" id="branchCountry" name="country" required>
                        <div class="error-message" id="countryError"></div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="branchPhone">Phone</label>
                        <input type="tel" id="branchPhone" name="phone">
                        <div class="error-message" id="phoneError"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="branchEmail">Email</label>
                        <input type="email" id="branchEmail" name="email">
                        <div class="error-message" id="emailError"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Working Hours *</label>
                    <div class="working-hours-form">
                        <?php 
                        $days = [
                            'monday' => 'Monday',
                            'tuesday' => 'Tuesday', 
                            'wednesday' => 'Wednesday',
                            'thursday' => 'Thursday',
                            'friday' => 'Friday',
                            'saturday' => 'Saturday',
                            'sunday' => 'Sunday'
                        ];
                        foreach ($days as $key => $day): 
                        ?>
                            <div class="day-hours-form">
                                <div class="day-label"><?php echo $day; ?></div>
                                <div class="hours-inputs">
                                    <label>
                                        <input type="checkbox" name="working_hours[<?php echo $key; ?>][closed]" 
                                               onchange="toggleDayHours('<?php echo $key; ?>')"> Closed
                                    </label>
                                    <div class="time-inputs" id="timeInputs<?php echo ucfirst($key); ?>">
                                        <input type="time" name="working_hours[<?php echo $key; ?>][open]" placeholder="Open">
                                        <span>to</span>
                                        <input type="time" name="working_hours[<?php echo $key; ?>][close]" placeholder="Close">
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="error-message" id="workingHoursError"></div>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeBranchModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="branchSubmitBtn">Add Branch</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Service Modal -->
    <div id="serviceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="serviceModalTitle">Add New Service</h3>
                <span class="close" onclick="closeServiceModal()">&times;</span>
            </div>
            
            <form id="serviceForm" onsubmit="submitServiceForm(event)">
                <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
                <input type="hidden" name="action" value="add_service">
                <input type="hidden" name="service_id" id="serviceId">
                
                <div class="form-group">
                    <label for="serviceBranch">Branch *</label>
                    <select id="serviceBranch" name="branch_id" required>
                        <option value="">Select a branch</option>
                        <?php foreach ($branches as $branch): ?>
                            <option value="<?php echo $branch['id']; ?>">
                                <?php echo htmlspecialchars($branch['branch_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-message" id="branchIdError"></div>
                </div>
                
                <div class="form-group">
                    <label for="serviceName">Service Name *</label>
                    <input type="text" id="serviceName" name="name" required>
                    <div class="error-message" id="nameError"></div>
                </div>
                
                <div class="form-group">
                    <label for="serviceDescription">Description</label>
                    <textarea id="serviceDescription" name="description" rows="3"></textarea>
                    <div class="error-message" id="descriptionError"></div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="serviceDuration">Duration (minutes) *</label>
                        <input type="number" id="serviceDuration" name="duration_minutes" min="1" required>
                        <div class="error-message" id="durationMinutesError"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="servicePrice">Price ($) *</label>
                        <input type="number" id="servicePrice" name="price" step="0.01" min="0" required>
                        <div class="error-message" id="priceError"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="serviceCategory">Category</label>
                    <select id="serviceCategory" name="category">
                        <option value="">Select category</option>
                        <option value="Hair">Hair</option>
                        <option value="Nails">Nails</option>
                        <option value="Facial">Facial</option>
                        <option value="Massage">Massage</option>
                        <option value="Makeup">Makeup</option>
                        <option value="Waxing">Waxing</option>
                        <option value="Other">Other</option>
                    </select>
                    <div class="error-message" id="categoryError"></div>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeServiceModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="serviceSubmitBtn">Add Service</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Staff Modal -->
    <div id="staffModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="staffModalTitle">Add New Staff Member</h3>
                <span class="close" onclick="closeStaffModal()">&times;</span>
            </div>
            
            <form id="staffForm" onsubmit="submitStaffForm(event)">
                <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
                <input type="hidden" name="action" value="add_staff">
                <input type="hidden" name="staff_id" id="staffId">
                
                <div class="form-group">
                    <label for="staffBranch">Branch *</label>
                    <select id="staffBranch" name="branch_id" required>
                        <option value="">Select a branch</option>
                        <?php foreach ($branches as $branch): ?>
                            <option value="<?php echo $branch['id']; ?>">
                                <?php echo htmlspecialchars($branch['branch_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-message" id="staffBranchIdError"></div>
                </div>
                
                <div class="form-group">
                    <label for="staffName">Staff Name *</label>
                    <input type="text" id="staffName" name="name" required>
                    <div class="error-message" id="staffNameError"></div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="staffEmail">Email</label>
                        <input type="email" id="staffEmail" name="email">
                        <div class="error-message" id="staffEmailError"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="staffPhone">Phone</label>
                        <input type="tel" id="staffPhone" name="phone">
                        <div class="error-message" id="staffPhoneError"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="staffSpecialties">Specialties</label>
                    <div class="specialties-input">
                        <div class="checkbox-group">
                            <label><input type="checkbox" name="specialties[]" value="Hair Cutting"> Hair Cutting</label>
                            <label><input type="checkbox" name="specialties[]" value="Hair Coloring"> Hair Coloring</label>
                            <label><input type="checkbox" name="specialties[]" value="Hair Styling"> Hair Styling</label>
                            <label><input type="checkbox" name="specialties[]" value="Manicure"> Manicure</label>
                            <label><input type="checkbox" name="specialties[]" value="Pedicure"> Pedicure</label>
                            <label><input type="checkbox" name="specialties[]" value="Facial Treatment"> Facial Treatment</label>
                            <label><input type="checkbox" name="specialties[]" value="Massage"> Massage</label>
                            <label><input type="checkbox" name="specialties[]" value="Makeup"> Makeup</label>
                            <label><input type="checkbox" name="specialties[]" value="Eyebrow Threading"> Eyebrow Threading</label>
                            <label><input type="checkbox" name="specialties[]" value="Waxing"> Waxing</label>
                        </div>
                    </div>
                    <div class="error-message" id="specialtiesError"></div>
                </div>
                
                <div class="form-group">
                    <label>Working Hours *</label>
                    <div class="working-hours-form">
                        <?php 
                        $days = [
                            'monday' => 'Monday',
                            'tuesday' => 'Tuesday', 
                            'wednesday' => 'Wednesday',
                            'thursday' => 'Thursday',
                            'friday' => 'Friday',
                            'saturday' => 'Saturday',
                            'sunday' => 'Sunday'
                        ];
                        foreach ($days as $key => $day): 
                        ?>
                            <div class="day-hours-form">
                                <div class="day-label"><?php echo $day; ?></div>
                                <div class="hours-inputs">
                                    <label>
                                        <input type="checkbox" name="working_hours[<?php echo $key; ?>][closed]" 
                                               onchange="toggleStaffDayHours('<?php echo $key; ?>')"> Off
                                    </label>
                                    <div class="time-inputs" id="staffTimeInputs<?php echo ucfirst($key); ?>">
                                        <input type="time" name="working_hours[<?php echo $key; ?>][open]" placeholder="Start">
                                        <span>to</span>
                                        <input type="time" name="working_hours[<?php echo $key; ?>][close]" placeholder="End">
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="error-message" id="staffWorkingHoursError"></div>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeStaffModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="staffSubmitBtn">Add Staff Member</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script>
        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.dashboard-section');
            
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all links and sections
                    navLinks.forEach(l => l.classList.remove('active'));
                    sections.forEach(s => s.classList.remove('active'));
                    
                    // Add active class to clicked link
                    this.classList.add('active');
                    
                    // Show corresponding section
                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);
                    if (targetSection) {
                        targetSection.classList.add('active');
                    }
                });
            });
        });

        // Branch Management Functions
        function showAddBranchModal() {
            document.getElementById('branchModalTitle').textContent = 'Add New Branch';
            document.getElementById('branchForm').querySelector('input[name="action"]').value = 'add_branch';
            document.getElementById('branchSubmitBtn').textContent = 'Add Branch';
            document.getElementById('branchForm').reset();
            document.getElementById('branchId').value = '';
            clearErrors();
            document.getElementById('branchModal').style.display = 'block';
        }

        function editBranch(branchId) {
            // Get branch data from the card
            const branchCard = document.querySelector(`[data-branch-id="${branchId}"]`);
            if (!branchCard) return;
            
            // This would typically fetch data via AJAX, but for now we'll use a simplified approach
            document.getElementById('branchModalTitle').textContent = 'Edit Branch';
            document.getElementById('branchForm').querySelector('input[name="action"]').value = 'edit_branch';
            document.getElementById('branchSubmitBtn').textContent = 'Update Branch';
            document.getElementById('branchId').value = branchId;
            clearErrors();
            
            // Show modal
            document.getElementById('branchModal').style.display = 'block';
            
            // Note: In a full implementation, you would fetch the branch data via AJAX
            // and populate the form fields
        }

        function deleteBranch(branchId) {
            if (!confirm('Are you sure you want to delete this branch? This action cannot be undone.')) {
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'delete_branch');
            formData.append('branch_id', branchId);
            formData.append('csrf_token', '<?php echo csrfToken(); ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    location.reload();
                } else {
                    showMessage('error', data.message);
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred. Please try again.');
            });
        }

        function closeBranchModal() {
            document.getElementById('branchModal').style.display = 'none';
        }

        function submitBranchForm(event) {
            event.preventDefault();
            clearErrors();
            
            const formData = new FormData(event.target);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    closeBranchModal();
                    location.reload();
                } else {
                    if (data.errors) {
                        displayErrors(data.errors);
                    } else {
                        showMessage('error', data.message);
                    }
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred. Please try again.');
            });
        }

        // Service Management Functions
        function showAddServiceModal() {
            document.getElementById('serviceModalTitle').textContent = 'Add New Service';
            document.getElementById('serviceForm').querySelector('input[name="action"]').value = 'add_service';
            document.getElementById('serviceSubmitBtn').textContent = 'Add Service';
            document.getElementById('serviceForm').reset();
            document.getElementById('serviceId').value = '';
            clearErrors();
            document.getElementById('serviceModal').style.display = 'block';
        }

        function editService(serviceId) {
            document.getElementById('serviceModalTitle').textContent = 'Edit Service';
            document.getElementById('serviceForm').querySelector('input[name="action"]').value = 'edit_service';
            document.getElementById('serviceSubmitBtn').textContent = 'Update Service';
            document.getElementById('serviceId').value = serviceId;
            clearErrors();
            
            // Show modal
            document.getElementById('serviceModal').style.display = 'block';
            
            // Note: In a full implementation, you would fetch the service data via AJAX
            // and populate the form fields
        }

        function deleteService(serviceId) {
            if (!confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'delete_service');
            formData.append('service_id', serviceId);
            formData.append('csrf_token', '<?php echo csrfToken(); ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    location.reload();
                } else {
                    showMessage('error', data.message);
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred. Please try again.');
            });
        }

        function closeServiceModal() {
            document.getElementById('serviceModal').style.display = 'none';
        }

        function submitServiceForm(event) {
            event.preventDefault();
            clearErrors();
            
            const formData = new FormData(event.target);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    closeServiceModal();
                    location.reload();
                } else {
                    if (data.errors) {
                        displayErrors(data.errors);
                    } else {
                        showMessage('error', data.message);
                    }
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred. Please try again.');
            });
        }

        // Working hours toggle
        function toggleDayHours(day) {
            const checkbox = document.querySelector(`input[name="working_hours[${day}][closed]"]`);
            const timeInputs = document.getElementById(`timeInputs${day.charAt(0).toUpperCase() + day.slice(1)}`);
            
            if (checkbox.checked) {
                timeInputs.style.display = 'none';
                timeInputs.querySelectorAll('input').forEach(input => {
                    input.removeAttribute('required');
                    input.value = '';
                });
            } else {
                timeInputs.style.display = 'flex';
                timeInputs.querySelectorAll('input').forEach(input => {
                    input.setAttribute('required', 'required');
                });
            }
        }

        // Utility functions
        function clearErrors() {
            document.querySelectorAll('.error-message').forEach(el => {
                el.textContent = '';
                el.style.display = 'none';
            });
        }

        function displayErrors(errors) {
            for (const [field, message] of Object.entries(errors)) {
                const errorElement = document.getElementById(field + 'Error');
                if (errorElement) {
                    errorElement.textContent = message;
                    errorElement.style.display = 'block';
                }
            }
        }

        function showMessage(type, message) {
            // Create a temporary flash message
            const flashContainer = document.querySelector('.flash-messages') || createFlashContainer();
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            flashContainer.appendChild(alertDiv);
            
            // Remove after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        function createFlashContainer() {
            const container = document.createElement('div');
            container.className = 'flash-messages';
            document.querySelector('.dashboard-main').insertBefore(container, document.querySelector('.dashboard-main').firstChild);
            return container;
        }

        // Staff Management Functions
        function showAddStaffModal() {
            document.getElementById('staffModalTitle').textContent = 'Add New Staff Member';
            document.getElementById('staffForm').querySelector('input[name="action"]').value = 'add_staff';
            document.getElementById('staffSubmitBtn').textContent = 'Add Staff Member';
            document.getElementById('staffForm').reset();
            document.getElementById('staffId').value = '';
            clearErrors();
            document.getElementById('staffModal').style.display = 'block';
        }

        function editStaff(staffId) {
            document.getElementById('staffModalTitle').textContent = 'Edit Staff Member';
            document.getElementById('staffForm').querySelector('input[name="action"]').value = 'edit_staff';
            document.getElementById('staffSubmitBtn').textContent = 'Update Staff Member';
            document.getElementById('staffId').value = staffId;
            clearErrors();
            
            // Show modal
            document.getElementById('staffModal').style.display = 'block';
            
            // Note: In a full implementation, you would fetch the staff data via AJAX
            // and populate the form fields
        }

        function deleteStaff(staffId) {
            if (!confirm('Are you sure you want to delete this staff member? This action cannot be undone.')) {
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'delete_staff');
            formData.append('staff_id', staffId);
            formData.append('csrf_token', '<?php echo csrfToken(); ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    location.reload();
                } else {
                    showMessage('error', data.message);
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred. Please try again.');
            });
        }

        function closeStaffModal() {
            document.getElementById('staffModal').style.display = 'none';
        }

        function submitStaffForm(event) {
            event.preventDefault();
            clearErrors();
            
            const formData = new FormData(event.target);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    closeStaffModal();
                    location.reload();
                } else {
                    if (data.errors) {
                        displayErrors(data.errors);
                    } else {
                        showMessage('error', data.message);
                    }
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred. Please try again.');
            });
        }

        // Staff working hours toggle
        function toggleStaffDayHours(day) {
            const checkbox = document.querySelector(`input[name="working_hours[${day}][closed]"]`);
            const timeInputs = document.getElementById(`staffTimeInputs${day.charAt(0).toUpperCase() + day.slice(1)}`);
            
            if (checkbox.checked) {
                timeInputs.style.display = 'none';
                timeInputs.querySelectorAll('input').forEach(input => {
                    input.removeAttribute('required');
                    input.value = '';
                });
            } else {
                timeInputs.style.display = 'flex';
                timeInputs.querySelectorAll('input').forEach(input => {
                    input.setAttribute('required', 'required');
                });
            }
        }

        // Appointment Management Functions
        let currentView = 'day';
        let currentDate = new Date().toISOString().split('T')[0];
        let selectedAppointment = null;

        // Load appointments when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('appointments')) {
                loadAppointments();
            }
        });

        // View control handlers
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentView = this.dataset.view;
                loadAppointments();
            });
        });

        function loadAppointments() {
            const container = document.getElementById('appointmentsContainer');
            const loading = document.getElementById('appointmentsLoading');
            
            if (!container) return;
            
            loading.style.display = 'block';
            container.innerHTML = '<div class="loading-spinner" id="appointmentsLoading">Loading appointments...</div>';
            
            const formData = new FormData();
            formData.append('action', 'get_appointments');
            formData.append('date', currentDate);
            formData.append('view_type', currentView);
            formData.append('csrf_token', '<?php echo csrfToken(); ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                if (data.success) {
                    displayAppointments(data.appointments, data.view_type);
                } else {
                    container.innerHTML = '<div class="error-message">' + data.message + '</div>';
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                container.innerHTML = '<div class="error-message">Failed to load appointments</div>';
            });
        }

        function displayAppointments(appointments, viewType) {
            const container = document.getElementById('appointmentsContainer');
            
            if (appointments.length === 0) {
                container.innerHTML = '<div class="empty-state"><h3>No appointments found</h3><p>No appointments scheduled for the selected period.</p></div>';
                return;
            }
            
            let html = '';
            
            if (viewType === 'day') {
                html = displayDayView(appointments);
            } else if (viewType === 'week') {
                html = displayWeekView(appointments);
            } else if (viewType === 'month') {
                html = displayMonthView(appointments);
            }
            
            container.innerHTML = html;
        }

        function displayDayView(appointments) {
            let html = '<div class="day-view">';
            html += '<h3>Appointments for ' + formatDate(currentDate) + '</h3>';
            html += '<div class="appointments-list">';
            
            appointments.forEach(appointment => {
                html += createAppointmentCard(appointment);
            });
            
            html += '</div></div>';
            return html;
        }

        function displayWeekView(appointments) {
            let html = '<div class="week-view">';
            html += '<h3>Week of ' + formatDate(currentDate) + '</h3>';
            
            // Group appointments by date
            const groupedAppointments = {};
            appointments.forEach(appointment => {
                const date = appointment.appointment_date;
                if (!groupedAppointments[date]) {
                    groupedAppointments[date] = [];
                }
                groupedAppointments[date].push(appointment);
            });
            
            // Display each day
            Object.keys(groupedAppointments).sort().forEach(date => {
                html += '<div class="day-group">';
                html += '<h4>' + formatDate(date) + '</h4>';
                html += '<div class="appointments-list">';
                groupedAppointments[date].forEach(appointment => {
                    html += createAppointmentCard(appointment);
                });
                html += '</div></div>';
            });
            
            html += '</div>';
            return html;
        }

        function displayMonthView(appointments) {
            let html = '<div class="month-view">';
            html += '<h3>Month of ' + formatDate(currentDate) + '</h3>';
            
            // Group appointments by date
            const groupedAppointments = {};
            appointments.forEach(appointment => {
                const date = appointment.appointment_date;
                if (!groupedAppointments[date]) {
                    groupedAppointments[date] = [];
                }
                groupedAppointments[date].push(appointment);
            });
            
            // Display each day with appointments
            Object.keys(groupedAppointments).sort().forEach(date => {
                html += '<div class="day-group">';
                html += '<h4>' + formatDate(date) + ' (' + groupedAppointments[date].length + ' appointments)</h4>';
                html += '<div class="appointments-list">';
                groupedAppointments[date].forEach(appointment => {
                    html += createAppointmentCard(appointment);
                });
                html += '</div></div>';
            });
            
            html += '</div>';
            return html;
        }

        function createAppointmentCard(appointment) {
            const statusClass = 'status-' + appointment.status;
            const customerName = appointment.first_name + ' ' + appointment.last_name;
            
            return `
                <div class="appointment-card ${statusClass}" onclick="showAppointmentDetails(${appointment.id})">
                    <div class="appointment-time">
                        <strong>${formatTime(appointment.appointment_time)}</strong>
                        <span class="duration">(${appointment.duration_minutes} min)</span>
                    </div>
                    <div class="appointment-info">
                        <h4>${appointment.service_name}</h4>
                        <p><strong>Customer:</strong> ${customerName}</p>
                        <p><strong>Branch:</strong> ${appointment.branch_name}</p>
                        ${appointment.staff_name ? '<p><strong>Staff:</strong> ' + appointment.staff_name + '</p>' : ''}
                        <p><strong>Price:</strong> $${parseFloat(appointment.price).toFixed(2)}</p>
                    </div>
                    <div class="appointment-status">
                        <span class="status-badge ${statusClass}">${appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}</span>
                    </div>
                </div>
            `;
        }

        function showAppointmentDetails(appointmentId) {
            // Find appointment in current data
            const formData = new FormData();
            formData.append('action', 'get_appointments');
            formData.append('date', currentDate);
            formData.append('view_type', currentView);
            formData.append('csrf_token', '<?php echo csrfToken(); ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const appointment = data.appointments.find(a => a.id == appointmentId);
                    if (appointment) {
                        selectedAppointment = appointment;
                        displayAppointmentModal(appointment);
                    }
                }
            });
        }

        function displayAppointmentModal(appointment) {
            const customerName = appointment.first_name + ' ' + appointment.last_name;
            
            document.getElementById('appointmentDetails').innerHTML = `
                <div class="appointment-info-modal">
                    <h4>${appointment.service_name}</h4>
                    <p><strong>Date:</strong> ${formatDate(appointment.appointment_date)}</p>
                    <p><strong>Time:</strong> ${formatTime(appointment.appointment_time)}</p>
                    <p><strong>Duration:</strong> ${appointment.duration_minutes} minutes</p>
                    <p><strong>Customer:</strong> ${customerName}</p>
                    <p><strong>Email:</strong> ${appointment.email}</p>
                    ${appointment.phone ? '<p><strong>Phone:</strong> ' + appointment.phone + '</p>' : ''}
                    <p><strong>Branch:</strong> ${appointment.branch_name}</p>
                    ${appointment.staff_name ? '<p><strong>Staff:</strong> ' + appointment.staff_name + '</p>' : ''}
                    <p><strong>Price:</strong> $${parseFloat(appointment.price).toFixed(2)}</p>
                    <p><strong>Status:</strong> <span class="status-badge status-${appointment.status}">${appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}</span></p>
                    ${appointment.notes ? '<p><strong>Notes:</strong> ' + appointment.notes + '</p>' : ''}
                </div>
            `;
            
            // Show/hide action buttons based on status
            const confirmBtn = document.getElementById('confirmBtn');
            const rescheduleBtn = document.getElementById('rescheduleBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            
            confirmBtn.style.display = appointment.status === 'pending' ? 'inline-block' : 'none';
            rescheduleBtn.style.display = (appointment.status === 'pending' || appointment.status === 'confirmed') ? 'inline-block' : 'none';
            cancelBtn.style.display = (appointment.status === 'pending' || appointment.status === 'confirmed') ? 'inline-block' : 'none';
            
            document.getElementById('appointmentModal').style.display = 'block';
        }

        function confirmAppointment() {
            if (!selectedAppointment) return;
            
            const formData = new FormData();
            formData.append('action', 'confirm_appointment');
            formData.append('appointment_id', selectedAppointment.id);
            formData.append('csrf_token', '<?php echo csrfToken(); ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    closeAppointmentModal();
                    loadAppointments();
                } else {
                    showMessage('error', data.message);
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred. Please try again.');
            });
        }

        function showRescheduleForm() {
            hideActionForms();
            document.getElementById('rescheduleForm').style.display = 'block';
            
            // Set minimum date to today
            document.getElementById('newDate').min = new Date().toISOString().split('T')[0];
        }

        function showCancelForm() {
            hideActionForms();
            document.getElementById('cancelForm').style.display = 'block';
        }

        function hideActionForms() {
            document.getElementById('rescheduleForm').style.display = 'none';
            document.getElementById('cancelForm').style.display = 'none';
        }

        function submitReschedule() {
            if (!selectedAppointment) return;
            
            const newDate = document.getElementById('newDate').value;
            const newTime = document.getElementById('newTime').value;
            
            if (!newDate || !newTime) {
                showMessage('error', 'Please select both date and time');
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'reschedule_appointment');
            formData.append('appointment_id', selectedAppointment.id);
            formData.append('new_date', newDate);
            formData.append('new_time', newTime);
            formData.append('csrf_token', '<?php echo csrfToken(); ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    closeAppointmentModal();
                    loadAppointments();
                } else {
                    showMessage('error', data.message);
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred. Please try again.');
            });
        }

        function submitCancel() {
            if (!selectedAppointment) return;
            
            const reason = document.getElementById('cancelReason').value;
            
            const formData = new FormData();
            formData.append('action', 'cancel_appointment');
            formData.append('appointment_id', selectedAppointment.id);
            formData.append('reason', reason);
            formData.append('csrf_token', '<?php echo csrfToken(); ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    closeAppointmentModal();
                    loadAppointments();
                } else {
                    showMessage('error', data.message);
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred. Please try again.');
            });
        }

        function closeAppointmentModal() {
            document.getElementById('appointmentModal').style.display = 'none';
            hideActionForms();
            selectedAppointment = null;
        }

        function navigateDate(direction) {
            const date = new Date(currentDate);
            
            if (currentView === 'day') {
                date.setDate(date.getDate() + direction);
            } else if (currentView === 'week') {
                date.setDate(date.getDate() + (direction * 7));
            } else if (currentView === 'month') {
                date.setMonth(date.getMonth() + direction);
            }
            
            currentDate = date.toISOString().split('T')[0];
            document.getElementById('appointmentDate').value = currentDate;
            loadAppointments();
        }

        function goToToday() {
            currentDate = new Date().toISOString().split('T')[0];
            document.getElementById('appointmentDate').value = currentDate;
            loadAppointments();
        }

        // Date input change handler
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.getElementById('appointmentDate');
            if (dateInput) {
                dateInput.addEventListener('change', function() {
                    currentDate = this.value;
                    loadAppointments();
                });
            }
        });

        // Utility functions
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        }

        function formatTime(timeString) {
            const time = new Date('2000-01-01 ' + timeString);
            return time.toLocaleTimeString('en-US', { 
                hour: 'numeric', 
                minute: '2-digit',
                hour12: true 
            });
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const branchModal = document.getElementById('branchModal');
            const serviceModal = document.getElementById('serviceModal');
            const staffModal = document.getElementById('staffModal');
            const appointmentModal = document.getElementById('appointmentModal');
            
            if (event.target === branchModal) {
                closeBranchModal();
            }
            if (event.target === serviceModal) {
                closeServiceModal();
            }
            if (event.target === staffModal) {
                closeStaffModal();
            }
            if (event.target === appointmentModal) {
                closeAppointmentModal();
            }
        }
    </script>
</body>
</html>