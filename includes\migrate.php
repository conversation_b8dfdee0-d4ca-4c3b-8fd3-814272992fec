<?php
/**
 * Database Migration Script
 * Handles database schema updates and versioning
 */

require_once 'config.php';
require_once 'database.php';

class DatabaseMigration {
    private $db;
    private $errors = [];
    private $success = [];
    private $migrationsTable = 'schema_migrations';
    
    public function __construct() {
        try {
            $this->db = new DatabaseQuery();
            $this->ensureMigrationsTable();
        } catch (Exception $e) {
            $this->errors[] = "Migration initialization failed: " . $e->getMessage();
        }
    }
    
    /**
     * Create migrations tracking table if it doesn't exist
     */
    private function ensureMigrationsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS {$this->migrationsTable} (
            id INT PRIMARY KEY AUTO_INCREMENT,
            version VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            execution_time_ms INT,
            INDEX idx_version (version)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }
    
    /**
     * Get all available migrations
     */
    private function getAvailableMigrations() {
        return [
            '001_initial_schema' => [
                'description' => 'Initial database schema creation',
                'up' => [$this, 'migration_001_up'],
                'down' => [$this, 'migration_001_down']
            ],
            '002_add_indexes' => [
                'description' => 'Add performance indexes',
                'up' => [$this, 'migration_002_up'],
                'down' => [$this, 'migration_002_down']
            ],
            '003_add_admin_logs' => [
                'description' => 'Add admin activity logging',
                'up' => [$this, 'migration_003_up'],
                'down' => [$this, 'migration_003_down']
            ],
            '004_add_password_reset' => [
                'description' => 'Add password reset functionality',
                'up' => [$this, 'migration_004_up'],
                'down' => [$this, 'migration_004_down']
            ],
            '005_add_sessions_table' => [
                'description' => 'Add user sessions management',
                'up' => [$this, 'migration_005_up'],
                'down' => [$this, 'migration_005_down']
            ]
        ];
    }
    
    /**
     * Get executed migrations
     */
    private function getExecutedMigrations() {
        try {
            $results = $this->db->select($this->migrationsTable, [], [], 'version ASC');
            return array_column($results, 'version');
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Run pending migrations
     */
    public function migrate() {
        $available = $this->getAvailableMigrations();
        $executed = $this->getExecutedMigrations();
        $pending = array_diff(array_keys($available), $executed);
        
        if (empty($pending)) {
            $this->success[] = "No pending migrations";
            return true;
        }
        
        foreach ($pending as $version) {
            if ($this->runMigration($version, $available[$version])) {
                $this->success[] = "Executed migration: {$version}";
            } else {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Run a single migration
     */
    private function runMigration($version, $migration) {
        try {
            $startTime = microtime(true);
            
            $this->db->beginTransaction();
            
            // Execute migration
            call_user_func($migration['up']);
            
            // Record migration
            $executionTime = round((microtime(true) - $startTime) * 1000);
            $this->db->insert($this->migrationsTable, [
                'version' => $version,
                'description' => $migration['description'],
                'execution_time_ms' => $executionTime
            ]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->errors[] = "Migration {$version} failed: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Rollback last migration
     */
    public function rollback($steps = 1) {
        $executed = $this->db->select($this->migrationsTable, [], [], 'executed_at DESC', $steps);
        $available = $this->getAvailableMigrations();
        
        foreach ($executed as $migration) {
            $version = $migration['version'];
            
            if (!isset($available[$version])) {
                $this->errors[] = "Migration {$version} not found for rollback";
                continue;
            }
            
            try {
                $this->db->beginTransaction();
                
                // Execute rollback
                call_user_func($available[$version]['down']);
                
                // Remove migration record
                $this->db->delete($this->migrationsTable, ['version' => $version]);
                
                $this->db->commit();
                $this->success[] = "Rolled back migration: {$version}";
                
            } catch (Exception $e) {
                $this->db->rollback();
                $this->errors[] = "Rollback {$version} failed: " . $e->getMessage();
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Get migration status
     */
    public function getStatus() {
        $available = $this->getAvailableMigrations();
        $executed = $this->getExecutedMigrations();
        
        $status = [];
        foreach ($available as $version => $migration) {
            $status[] = [
                'version' => $version,
                'description' => $migration['description'],
                'executed' => in_array($version, $executed)
            ];
        }
        
        return $status;
    }
    
    // Migration 001: Initial schema
    private function migration_001_up() {
        $schemaFile = __DIR__ . '/schema.sql';
        if (!file_exists($schemaFile)) {
            throw new Exception("Schema file not found");
        }
        
        $sql = file_get_contents($schemaFile);
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^--/', $stmt);
            }
        );
        
        foreach ($statements as $statement) {
            if (!empty(trim($statement))) {
                $this->db->query($statement);
            }
        }
    }
    
    private function migration_001_down() {
        $tables = [
            'user_sessions', 'password_reset_tokens', 'admin_logs', 'reviews', 
            'appointments', 'staff', 'services', 'shop_branches', 'shops', 'users'
        ];
        
        foreach ($tables as $table) {
            $this->db->query("DROP TABLE IF EXISTS {$table}");
        }
    }
    
    // Migration 002: Add indexes
    private function migration_002_up() {
        $indexes = [
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
            "CREATE INDEX IF NOT EXISTS idx_shops_status ON shops(status)",
            "CREATE INDEX IF NOT EXISTS idx_branches_location ON shop_branches(city, country)",
            "CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date, appointment_time)",
            "CREATE INDEX IF NOT EXISTS idx_reviews_shop ON reviews(shop_id, status)"
        ];
        
        foreach ($indexes as $index) {
            try {
                $this->db->query($index);
            } catch (Exception $e) {
                // Log but don't fail on index creation issues
                error_log("Index creation warning: " . $e->getMessage());
            }
        }
    }
    
    private function migration_002_down() {
        $indexes = [
            "DROP INDEX IF EXISTS idx_users_email ON users",
            "DROP INDEX IF EXISTS idx_users_role ON users",
            "DROP INDEX IF EXISTS idx_shops_status ON shops",
            "DROP INDEX IF EXISTS idx_branches_location ON shop_branches",
            "DROP INDEX IF EXISTS idx_appointments_date ON appointments",
            "DROP INDEX IF EXISTS idx_reviews_shop ON reviews"
        ];
        
        foreach ($indexes as $index) {
            try {
                $this->db->query($index);
            } catch (Exception $e) {
                // Ignore errors for non-existent indexes
            }
        }
    }
    
    // Migration 003: Add admin logs
    private function migration_003_up() {
        $sql = "CREATE TABLE IF NOT EXISTS admin_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            admin_id INT NOT NULL,
            action VARCHAR(255) NOT NULL,
            target_type ENUM('user', 'shop', 'review', 'appointment') NOT NULL,
            target_id INT NOT NULL,
            details JSON,
            ip_address VARCHAR(45),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_admin (admin_id),
            INDEX idx_action (action),
            INDEX idx_target (target_type, target_id),
            INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }
    
    private function migration_003_down() {
        $this->db->query("DROP TABLE IF EXISTS admin_logs");
    }
    
    // Migration 004: Add password reset
    private function migration_004_up() {
        $sql = "CREATE TABLE IF NOT EXISTS password_reset_tokens (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            token VARCHAR(255) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            used BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user (user_id),
            INDEX idx_token (token),
            INDEX idx_expires (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }
    
    private function migration_004_down() {
        $this->db->query("DROP TABLE IF EXISTS password_reset_tokens");
    }
    
    // Migration 005: Add sessions table
    private function migration_005_up() {
        $sql = "CREATE TABLE IF NOT EXISTS user_sessions (
            id VARCHAR(128) PRIMARY KEY,
            user_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user (user_id),
            INDEX idx_last_activity (last_activity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }
    
    private function migration_005_down() {
        $this->db->query("DROP TABLE IF EXISTS user_sessions");
    }
    
    /**
     * Get errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get success messages
     */
    public function getSuccess() {
        return $this->success;
    }
    
    /**
     * Display results
     */
    public function displayResults() {
        if (!empty($this->success)) {
            echo "<div style='color: green; margin: 10px 0;'>\n";
            echo "<h3>✓ Migration Success</h3>\n";
            foreach ($this->success as $message) {
                echo "<p>✓ " . htmlspecialchars($message) . "</p>\n";
            }
            echo "</div>\n";
        }
        
        if (!empty($this->errors)) {
            echo "<div style='color: red; margin: 10px 0;'>\n";
            echo "<h3>✗ Migration Errors</h3>\n";
            foreach ($this->errors as $error) {
                echo "<p>✗ " . htmlspecialchars($error) . "</p>\n";
            }
            echo "</div>\n";
        }
    }
}

// Command line interface for migration operations
if (basename($_SERVER['PHP_SELF']) === 'migrate.php' && php_sapi_name() === 'cli') {
    $migration = new DatabaseMigration();
    
    if ($argc < 2) {
        echo "Usage: php migrate.php [migrate|rollback|status]\n";
        echo "  migrate           - Run pending migrations\n";
        echo "  rollback [steps]  - Rollback migrations (default: 1 step)\n";
        echo "  status            - Show migration status\n";
        exit(1);
    }
    
    $command = $argv[1];
    
    switch ($command) {
        case 'migrate':
            $migration->migrate();
            break;
            
        case 'rollback':
            $steps = isset($argv[2]) ? (int)$argv[2] : 1;
            $migration->rollback($steps);
            break;
            
        case 'status':
            $status = $migration->getStatus();
            echo "Migration Status:\n";
            echo str_repeat('-', 60) . "\n";
            foreach ($status as $migration) {
                $status = $migration['executed'] ? '✓ Executed' : '✗ Pending';
                echo sprintf("%-20s %s - %s\n", 
                    $migration['version'], 
                    $status, 
                    $migration['description']
                );
            }
            break;
            
        default:
            echo "Unknown command: {$command}\n";
            exit(1);
    }
    
    $migration->displayResults();
}

// Web interface for migration operations
if (basename($_SERVER['PHP_SELF']) === 'migrate.php' && php_sapi_name() !== 'cli') {
    $migration = new DatabaseMigration();
    
    if (isset($_POST['action'])) {
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
            $migration->errors[] = 'Security token mismatch. Please try again.';
        } else {
            switch ($_POST['action']) {
                case 'migrate':
                    $migration->migrate();
                    break;
                case 'rollback':
                    $steps = isset($_POST['steps']) ? (int)$_POST['steps'] : 1;
                    $migration->rollback($steps);
                    break;
            }
        }
    }
    
    $status = $migration->getStatus();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Database Migration Manager</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { border-collapse: collapse; width: 100%; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .executed { color: green; }
            .pending { color: orange; }
            .button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        </style>
    </head>
    <body>
        <h1>Database Migration Manager</h1>
        
        <?php $migration->displayResults(); ?>
        
        <h2>Migration Status</h2>
        <table>
            <tr>
                <th>Version</th>
                <th>Description</th>
                <th>Status</th>
            </tr>
            <?php foreach ($status as $mig): ?>
            <tr>
                <td><?php echo htmlspecialchars($mig['version']); ?></td>
                <td><?php echo htmlspecialchars($mig['description']); ?></td>
                <td class="<?php echo $mig['executed'] ? 'executed' : 'pending'; ?>">
                    <?php echo $mig['executed'] ? '✓ Executed' : '✗ Pending'; ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </table>
        
        <h2>Actions</h2>
        <form method="post" style="display: inline;">
            <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
            <input type="hidden" name="action" value="migrate">
            <input type="submit" value="Run Migrations" class="button">
        </form>
        
        <form method="post" style="display: inline;">
            <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
            <input type="hidden" name="action" value="rollback">
            <input type="number" name="steps" value="1" min="1" max="10" style="width: 50px;">
            <input type="submit" value="Rollback" class="button">
        </form>
    </body>
    </html>
    <?php
}
?>