<?php
/**
 * API endpoint to get user details for admin panel
 */

require_once '../includes/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Require admin authentication
if (!hasRole('admin')) {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied']);
    exit;
}

// Check if user ID is provided
if (!isset($_GET['user_id']) || !validateId($_GET['user_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid user ID']);
    exit;
}

$userId = $_GET['user_id'];

try {
    $userDetails = getUserDetails($userId);
    
    if (!$userDetails) {
        http_response_code(404);
        echo json_encode(['error' => 'User not found']);
        exit;
    }
    
    // Remove sensitive information
    unset($userDetails['password_hash']);
    
    // Format dates for display
    $userDetails['created_at_formatted'] = formatDate($userDetails['created_at']);
    $userDetails['updated_at_formatted'] = formatDate($userDetails['updated_at']);
    
    echo json_encode([
        'success' => true,
        'user' => $userDetails
    ]);
    
} catch (Exception $e) {
    logError('User details API error: ' . $e->getMessage(), ['user_id' => $userId]);
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>