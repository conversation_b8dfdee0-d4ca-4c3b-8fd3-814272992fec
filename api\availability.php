<?php
/**
 * Availability API - Beauty Platform
 * Handles real-time availability checking for appointment booking
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Set security headers
setSecurityHeaders();
header('Content-Type: application/json');

// Rate limiting
$clientId = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
if (!checkRateLimit('api_availability', $clientId, 30, 300)) { // 30 requests per 5 minutes
    http_response_code(429);
    echo json_encode(['error' => 'Rate limit exceeded']);
    exit();
}

// Require customer authentication
if (!isLoggedIn() || !hasRole('customer')) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Get JSON input
$rawInput = file_get_contents('php://input');
$input = json_decode($rawInput, true);

// Validate JSON input
if (json_last_error() !== JSON_ERROR_NONE) {
    recordRateLimitAttempt('api_availability', $clientId);
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON input']);
    exit();
}

// Verify CSRF token
if (!isset($input['csrf_token']) || !verifyCsrfToken($input['csrf_token'])) {
    recordRateLimitAttempt('api_availability', $clientId);
    logSecurityEvent('api_csrf_failure', [
        'endpoint' => 'availability',
        'user_id' => getCurrentUserId(),
        'ip' => $clientId
    ]);
    http_response_code(403);
    echo json_encode(['error' => 'Invalid security token']);
    exit();
}

// Validate required parameters
if (!isset($input['service_id']) || !isset($input['date'])) {
    recordRateLimitAttempt('api_availability', $clientId);
    http_response_code(400);
    echo json_encode(['error' => 'Missing required parameters']);
    exit();
}

$serviceId = sanitizeInt($input['service_id']);
$date = sanitizeString($input['date']);
$staffId = isset($input['staff_id']) ? sanitizeInt($input['staff_id']) : null;

// Validate inputs
if (!validateId($serviceId) || !validateDate($date)) {
    recordRateLimitAttempt('api_availability', $clientId);
    logSecurityEvent('api_invalid_input', [
        'endpoint' => 'availability',
        'service_id' => $input['service_id'] ?? 'missing',
        'date' => $input['date'] ?? 'missing',
        'user_id' => getCurrentUserId()
    ]);
    http_response_code(400);
    echo json_encode(['error' => 'Invalid parameters']);
    exit();
}

// Check if date is not in the past
if (strtotime($date) < strtotime(date('Y-m-d'))) {
    recordRateLimitAttempt('api_availability', $clientId);
    http_response_code(400);
    echo json_encode(['error' => 'Date cannot be in the past']);
    exit();
}

// Check if date is not too far in the future (max 3 months)
if (strtotime($date) > strtotime('+3 months')) {
    recordRateLimitAttempt('api_availability', $clientId);
    http_response_code(400);
    echo json_encode(['error' => 'Date too far in the future']);
    exit();
}

try {
    $db = new DatabaseQuery();
    
    // Get service information with branch details
    $service = $db->queryOne("
        SELECT 
            s.*,
            sb.working_hours,
            sb.branch_name,
            sh.business_name
        FROM services s
        INNER JOIN shop_branches sb ON s.branch_id = sb.id
        INNER JOIN shops sh ON sb.shop_id = sh.id
        WHERE s.id = :service_id 
        AND s.status = 'active' 
        AND sb.status = 'active' 
        AND sh.status = 'approved'
    ", [':service_id' => $serviceId]);

    if (!$service) {
        http_response_code(404);
        echo json_encode(['error' => 'Service not found']);
        exit();
    }

    // Get working hours for the selected date
    $workingHours = parseWorkingHours($service['working_hours']);
    $dayOfWeek = strtolower(date('l', strtotime($date)));
    
    if (!isset($workingHours[$dayOfWeek]) || 
        (isset($workingHours[$dayOfWeek]['closed']) && $workingHours[$dayOfWeek]['closed'])) {
        echo json_encode([
            'success' => true,
            'available_slots' => [],
            'message' => 'Shop is closed on this day'
        ]);
        exit();
    }

    $dayHours = $workingHours[$dayOfWeek];
    if (!isset($dayHours['open']) || !isset($dayHours['close'])) {
        echo json_encode([
            'success' => true,
            'available_slots' => [],
            'message' => 'Working hours not properly configured'
        ]);
        exit();
    }

    // Generate time slots
    $timeSlots = generateTimeSlots(
        $dayHours['open'], 
        $dayHours['close'], 
        $service['duration_minutes']
    );

    // Check availability for each time slot
    $availableSlots = [];
    foreach ($timeSlots as $timeSlot) {
        $isAvailable = checkSlotAvailability($db, $serviceId, $date, $timeSlot, $staffId);
        if ($isAvailable) {
            $availableSlots[] = [
                'time' => $timeSlot,
                'formatted_time' => formatTime($timeSlot),
                'available' => true
            ];
        }
    }

    echo json_encode([
        'success' => true,
        'available_slots' => $availableSlots,
        'shop_hours' => [
            'open' => formatTime($dayHours['open']),
            'close' => formatTime($dayHours['close'])
        ],
        'message' => count($availableSlots) > 0 ? 
            'Available time slots found' : 
            'No available time slots for this date'
    ]);

} catch (Exception $e) {
    logError('Availability API error: ' . $e->getMessage(), [
        'service_id' => $serviceId,
        'date' => $date,
        'staff_id' => $staffId,
        'user_id' => getCurrentUserId()
    ]);
    
    http_response_code(500);
    echo json_encode(['error' => 'Unable to check availability']);
}

/**
 * Generate time slots based on working hours and service duration
 */
function generateTimeSlots($startTime, $endTime, $serviceDuration) {
    $slots = [];
    $start = new DateTime($startTime);
    $end = new DateTime($endTime);
    
    // Subtract service duration from end time to ensure appointments can be completed
    $end->sub(new DateInterval('PT' . $serviceDuration . 'M'));
    
    // Generate slots every 30 minutes (or service duration if shorter)
    $interval = min(30, $serviceDuration);
    
    while ($start <= $end) {
        $slots[] = $start->format('H:i');
        $start->add(new DateInterval('PT' . $interval . 'M'));
    }
    
    return $slots;
}

/**
 * Check if a specific time slot is available
 */
function checkSlotAvailability($db, $serviceId, $date, $time, $staffId = null) {
    try {
        // Build query conditions
        $conditions = [
            'appointment_date' => $date,
            'appointment_time' => $time
        ];

        // Check for confirmed appointments only
        $sql = "
            SELECT COUNT(*) as count 
            FROM appointments a
            WHERE a.appointment_date = :date 
            AND a.appointment_time = :time
            AND a.status IN ('pending', 'confirmed')
        ";
        
        $params = [
            ':date' => $date,
            ':time' => $time
        ];

        if ($staffId) {
            // Check specific staff member availability
            $sql .= " AND a.staff_id = :staff_id";
            $params[':staff_id'] = $staffId;
        } else {
            // Check if any appointment exists for this service at this time
            $sql .= " AND a.service_id = :service_id";
            $params[':service_id'] = $serviceId;
        }

        $result = $db->queryOne($sql, $params);
        return (int)$result['count'] === 0;

    } catch (Exception $e) {
        logError('Slot availability check error: ' . $e->getMessage(), [
            'service_id' => $serviceId,
            'date' => $date,
            'time' => $time,
            'staff_id' => $staffId
        ]);
        return false; // Err on the side of caution
    }
}
?>