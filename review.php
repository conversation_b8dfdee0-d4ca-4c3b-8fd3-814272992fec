<?php
/**
 * Review Submission Page - Beauty Platform
 * Allows customers to submit reviews for completed appointments
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Require customer authentication
requireRole('customer', '/login.php');

// Initialize database query helper
$db = new DatabaseQuery();
$currentUser = getCurrentUser(); 

// Get appointment ID from URL
$appointmentId = isset($_GET['appointment']) ? sanitizeInt($_GET['appointment']) : null;

if (!$appointmentId || !validateId($appointmentId)) {
    setFlashMessage('error', 'Invalid appointment specified.');
    redirect('/dashboard/customer.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $reviewResult = handleReviewSubmission($db, $_POST, $currentUser['id'], $appointmentId);
    
    if ($reviewResult['success']) {
        setFlashMessage('success', $reviewResult['message']);
        redirect('/dashboard/customer.php');
    } else {
        setFlashMessage('error', $reviewResult['message']);
    }
}

try {
    // Get appointment details
    $appointment = getAppointmentForReview($currentUser['id'], $appointmentId);
    
    if (!$appointment) {
        setFlashMessage('error', 'Appointment not found or not eligible for review.');
        redirect('/dashboard/customer.php');
    }
    
    // Check if customer can review this appointment
    $reviewCheck = canReviewAppointment($currentUser['id'], $appointmentId);
    if (!$reviewCheck['can_review']) {
        setFlashMessage('error', $reviewCheck['reason']);
        redirect('/dashboard/customer.php');
    }

} catch (Exception $e) {
    logError("Failed to load review page: " . $e->getMessage(), [
        'user_id' => $currentUser['id'],
        'appointment_id' => $appointmentId
    ]);
    setFlashMessage('error', 'Unable to load review page. Please try again.');
    redirect('/dashboard/customer.php');
}

/**
 * Handle review form submission
 */
function handleReviewSubmission($db, $postData, $customerId, $appointmentId) {
    try {
        // Validate CSRF token
        if (!isset($postData['csrf_token']) || !verifyCsrfToken($postData['csrf_token'])) {
            return [
                'success' => false,
                'message' => 'Invalid form submission. Please try again.'
            ];
        }

        $rating = isset($postData['rating']) ? (int)$postData['rating'] : null;
        $comment = isset($postData['comment']) ? trim($postData['comment']) : '';
        $shopId = isset($postData['shop_id']) ? (int)$postData['shop_id'] : null;

        // Validate rating
        if (!$rating || !validateRating($rating)) {
            return [
                'success' => false,
                'message' => 'Please select a rating between 1 and 5 stars.'
            ];
        }

        // Validate shop ID
        if (!$shopId || !validateId($shopId)) {
            return [
                'success' => false,
                'message' => 'Invalid shop information.'
            ];
        }

        // Validate comment length
        if (!empty($comment) && strlen($comment) > 1000) {
            return [
                'success' => false,
                'message' => 'Review comment must be less than 1000 characters.'
            ];
        }

        // Submit review
        return submitReview($customerId, $shopId, $appointmentId, $rating, $comment);

    } catch (Exception $e) {
        logError('Review submission error: ' . $e->getMessage(), $postData);
        return [
            'success' => false,
            'message' => 'Failed to submit review. Please try again.'
        ];
    }
}

$pageTitle = 'Leave a Review';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .review-form {
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .appointment-summary {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 6px;
            margin-bottom: 2rem;
        }
        
        .rating-input {
            margin: 1.5rem 0;
        }
        
        .star-rating {
            display: flex;
            gap: 0.5rem;
            margin: 0.5rem 0;
        }
        
        .star {
            font-size: 2rem;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .star:hover,
        .star.active {
            color: #ffc107;
        }
        
        .rating-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
            color: #666;
            margin-top: 0.5rem;
        }
        
        .comment-input {
            margin: 1.5rem 0;
        }
        
        .comment-input textarea {
            width: 100%;
            min-height: 120px;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            resize: vertical;
        }
        
        .char-counter {
            text-align: right;
            font-size: 0.875rem;
            color: #666;
            margin-top: 0.5rem;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="header-content">
                <div class="logo">
                    <h1><a href="index.php">Beauty Platform</a></h1>
                </div>
                <nav class="nav">
                    <a href="index.php" class="nav-link">Browse Shops</a>
                    <a href="dashboard/customer.php" class="nav-link">Dashboard</a>
                    <a href="logout.php" class="nav-link">Logout</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Breadcrumb -->
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="dashboard/customer.php">Dashboard</a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current">Leave Review</span>
            </nav>

            <!-- Flash Messages -->
            <?php
            $flashMessages = getFlashMessages();
            if (!empty($flashMessages)):
            ?>
                <?php foreach ($flashMessages as $message): ?>
                    <div class="alert alert-<?php echo $message['type']; ?>">
                        <?php echo htmlspecialchars($message['message']); ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- Review Form -->
            <div class="review-form">
                <h1>Leave a Review</h1>
                <p>Share your experience to help other customers make informed decisions.</p>

                <!-- Appointment Summary -->
                <div class="appointment-summary">
                    <h3><?php echo htmlspecialchars($appointment['business_name']); ?></h3>
                    <p><strong>Branch:</strong> <?php echo htmlspecialchars($appointment['branch_name']); ?></p>
                    <p><strong>Service:</strong> <?php echo htmlspecialchars($appointment['service_name']); ?></p>
                    <?php if (!empty($appointment['staff_name'])): ?>
                        <p><strong>Staff:</strong> <?php echo htmlspecialchars($appointment['staff_name']); ?></p>
                    <?php endif; ?>
                    <p><strong>Date:</strong> <?php echo formatDate($appointment['appointment_date']); ?> at <?php echo formatTime($appointment['appointment_time']); ?></p>
                    <p><strong>Duration:</strong> <?php echo $appointment['duration_minutes']; ?> minutes</p>
                    <p><strong>Price:</strong> <?php echo formatPrice($appointment['total_price']); ?></p>
                </div>

                <!-- Review Form -->
                <form method="POST" id="reviewForm">
                    <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
                    <input type="hidden" name="shop_id" value="<?php echo $appointment['shop_id']; ?>">

                    <!-- Rating Input -->
                    <div class="rating-input">
                        <label class="form-label">
                            <strong>Overall Rating *</strong>
                        </label>
                        <div class="star-rating" id="starRating">
                            <span class="star" data-rating="1">★</span>
                            <span class="star" data-rating="2">★</span>
                            <span class="star" data-rating="3">★</span>
                            <span class="star" data-rating="4">★</span>
                            <span class="star" data-rating="5">★</span>
                        </div>
                        <div class="rating-labels">
                            <span>Poor</span>
                            <span>Fair</span>
                            <span>Good</span>
                            <span>Very Good</span>
                            <span>Excellent</span>
                        </div>
                        <input type="hidden" name="rating" id="ratingValue" required>
                        <div id="ratingError" class="error-message" style="display: none;">
                            Please select a rating
                        </div>
                    </div>

                    <!-- Comment Input -->
                    <div class="comment-input">
                        <label for="comment" class="form-label">
                            <strong>Your Review (Optional)</strong>
                        </label>
                        <textarea 
                            id="comment" 
                            name="comment" 
                            placeholder="Tell us about your experience... What did you like? What could be improved?"
                            maxlength="1000"
                            oninput="updateCharCounter()"
                        ></textarea>
                        <div class="char-counter">
                            <span id="charCount">0</span>/1000 characters
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <a href="dashboard/customer.php" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Submit Review</button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Beauty Platform. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Star rating functionality
        const stars = document.querySelectorAll('.star');
        const ratingValue = document.getElementById('ratingValue');
        const ratingError = document.getElementById('ratingError');
        let selectedRating = 0;

        stars.forEach(star => {
            star.addEventListener('click', function() {
                selectedRating = parseInt(this.dataset.rating);
                ratingValue.value = selectedRating;
                updateStars();
                ratingError.style.display = 'none';
            });

            star.addEventListener('mouseover', function() {
                const hoverRating = parseInt(this.dataset.rating);
                highlightStars(hoverRating);
            });
        });

        document.getElementById('starRating').addEventListener('mouseleave', function() {
            updateStars();
        });

        function highlightStars(rating) {
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        function updateStars() {
            highlightStars(selectedRating);
        }

        // Character counter
        function updateCharCounter() {
            const comment = document.getElementById('comment');
            const charCount = document.getElementById('charCount');
            charCount.textContent = comment.value.length;
        }

        // Form validation
        document.getElementById('reviewForm').addEventListener('submit', function(e) {
            if (!selectedRating) {
                e.preventDefault();
                ratingError.style.display = 'block';
                document.getElementById('starRating').scrollIntoView({ behavior: 'smooth' });
            }
        });
    </script>
</body>
</html>