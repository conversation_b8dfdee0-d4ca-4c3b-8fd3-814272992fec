<?php
/**
 * Shop Registration Page
 * Handles business registration for shop owners
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Redirect if not logged in or not a shop owner
if (!isLoggedIn()) {
    redirect('/login.php');
}

$user = getCurrentUser();
if ($user['role'] !== 'shop_owner') {
    setFlashMessage('error', 'Access denied. Only shop owners can register businesses.');
    redirect('/');
}
 
// Check if user already has a shop
$db = new DatabaseQuery();
$existingShop = $db->selectOne('shops', ['owner_id' => $user['id']]);

if ($existingShop) {
    setFlashMessage('info', 'You already have a registered business.');
    redirect('/dashboard/shop-owner.php');
}

$errors = [];
$success = false;
$formData = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        $errors['general'] = 'Security token mismatch. Please try again.';
    } else {
        // Get form data
        $formData = [
            'business_name' => $_POST['business_name'] ?? '',
            'description' => $_POST['description'] ?? '',
            'business_license' => $_POST['business_license'] ?? '',
            'branch_name' => $_POST['branch_name'] ?? '',
            'address' => $_POST['address'] ?? '',
            'city' => $_POST['city'] ?? '',
            'country' => $_POST['country'] ?? '',
            'branch_phone' => $_POST['branch_phone'] ?? '',
            'branch_email' => $_POST['branch_email'] ?? '',
            'working_hours' => [
                'monday' => ['open' => $_POST['monday_open'] ?? '', 'close' => $_POST['monday_close'] ?? '', 'closed' => isset($_POST['monday_closed'])],
                'tuesday' => ['open' => $_POST['tuesday_open'] ?? '', 'close' => $_POST['tuesday_close'] ?? '', 'closed' => isset($_POST['tuesday_closed'])],
                'wednesday' => ['open' => $_POST['wednesday_open'] ?? '', 'close' => $_POST['wednesday_close'] ?? '', 'closed' => isset($_POST['wednesday_closed'])],
                'thursday' => ['open' => $_POST['thursday_open'] ?? '', 'close' => $_POST['thursday_close'] ?? '', 'closed' => isset($_POST['thursday_closed'])],
                'friday' => ['open' => $_POST['friday_open'] ?? '', 'close' => $_POST['friday_close'] ?? '', 'closed' => isset($_POST['friday_closed'])],
                'saturday' => ['open' => $_POST['saturday_open'] ?? '', 'close' => $_POST['saturday_close'] ?? '', 'closed' => isset($_POST['saturday_closed'])],
                'sunday' => ['open' => $_POST['sunday_open'] ?? '', 'close' => $_POST['sunday_close'] ?? '', 'closed' => isset($_POST['sunday_closed'])]
            ]
        ];
        
        // Attempt shop registration
        $result = registerShop($user['id'], $formData);
        
        if ($result['success']) {
            $success = true;
            setFlashMessage('success', 'Shop registration submitted successfully! Your business is pending admin approval.');
            
            // Clear form data on success
            $formData = [];
        } else {
            $errors = $result['errors'];
        }
    }
}

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register Your Business - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container">
        <div class="shop-register-wrapper">
            <div class="shop-register-card">
                <div class="shop-register-header">
                    <h1>Register Your Beauty Business</h1>
                    <p>Join our platform and start connecting with customers</p>
                </div>

                <?php if (!empty($flashMessages)): ?>
                    <div class="flash-messages">
                        <?php foreach ($flashMessages as $message): ?>
                            <div class="alert alert-<?php echo htmlspecialchars($message['type']); ?>">
                                <?php echo htmlspecialchars($message['message']); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="success-message">
                        <div class="alert alert-success">
                            <h3>Registration Submitted!</h3>
                            <p>Your business registration has been submitted successfully. Our admin team will review your application and notify you once it's approved.</p>
                            <div class="success-actions">
                                <a href="/dashboard/shop-owner.php" class="btn btn-primary">Go to Dashboard</a>
                                <a href="/" class="btn btn-secondary">Back to Home</a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <?php if (isset($errors['general'])): ?>
                        <div class="alert alert-error">
                            <?php echo htmlspecialchars($errors['general']); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="shop-register.php" class="shop-register-form" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
                        
                        <!-- Business Information Section -->
                        <div class="form-section">
                            <h2>Business Information</h2>
                            
                            <div class="form-group">
                                <label for="business_name">Business Name *</label>
                                <input type="text" id="business_name" name="business_name" 
                                       value="<?php echo htmlspecialchars($formData['business_name'] ?? ''); ?>" 
                                       required maxlength="255" placeholder="Enter your business name">
                                <?php if (isset($errors['business_name'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['business_name']); ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="form-group">
                                <label for="description">Business Description</label>
                                <textarea id="description" name="description" rows="4" maxlength="1000" 
                                          placeholder="Describe your business, services, and what makes you special"><?php echo htmlspecialchars($formData['description'] ?? ''); ?></textarea>
                                <small class="form-help">Optional - This will be displayed on your shop profile</small>
                                <?php if (isset($errors['description'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['description']); ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="form-group">
                                <label for="business_license">Business License Number</label>
                                <input type="text" id="business_license" name="business_license" 
                                       value="<?php echo htmlspecialchars($formData['business_license'] ?? ''); ?>" 
                                       maxlength="255" placeholder="Enter your business license number">
                                <small class="form-help">Optional - Helps build trust with customers</small>
                                <?php if (isset($errors['business_license'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['business_license']); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Branch Information Section -->
                        <div class="form-section">
                            <h2>Main Branch Information</h2>
                            
                            <div class="form-group">
                                <label for="branch_name">Branch Name *</label>
                                <input type="text" id="branch_name" name="branch_name" 
                                       value="<?php echo htmlspecialchars($formData['branch_name'] ?? ''); ?>" 
                                       required maxlength="255" placeholder="e.g., Downtown Location, Main Branch">
                                <?php if (isset($errors['branch_name'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['branch_name']); ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="form-group">
                                <label for="address">Address *</label>
                                <textarea id="address" name="address" rows="3" required maxlength="500" 
                                          placeholder="Enter the full address of your branch"><?php echo htmlspecialchars($formData['address'] ?? ''); ?></textarea>
                                <?php if (isset($errors['address'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['address']); ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="city">City *</label>
                                    <input type="text" id="city" name="city" 
                                           value="<?php echo htmlspecialchars($formData['city'] ?? ''); ?>" 
                                           required maxlength="100" placeholder="Enter city">
                                    <?php if (isset($errors['city'])): ?>
                                        <div class="error-message"><?php echo htmlspecialchars($errors['city']); ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="form-group">
                                    <label for="country">Country *</label>
                                    <input type="text" id="country" name="country" 
                                           value="<?php echo htmlspecialchars($formData['country'] ?? ''); ?>" 
                                           required maxlength="100" placeholder="Enter country">
                                    <?php if (isset($errors['country'])): ?>
                                        <div class="error-message"><?php echo htmlspecialchars($errors['country']); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="branch_phone">Branch Phone</label>
                                    <input type="tel" id="branch_phone" name="branch_phone" 
                                           value="<?php echo htmlspecialchars($formData['branch_phone'] ?? ''); ?>" 
                                           maxlength="20" placeholder="Branch phone number">
                                    <?php if (isset($errors['branch_phone'])): ?>
                                        <div class="error-message"><?php echo htmlspecialchars($errors['branch_phone']); ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="form-group">
                                    <label for="branch_email">Branch Email</label>
                                    <input type="email" id="branch_email" name="branch_email" 
                                           value="<?php echo htmlspecialchars($formData['branch_email'] ?? ''); ?>" 
                                           maxlength="255" placeholder="Branch email address">
                                    <?php if (isset($errors['branch_email'])): ?>
                                        <div class="error-message"><?php echo htmlspecialchars($errors['branch_email']); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Working Hours Section -->
                        <div class="form-section">
                            <h2>Working Hours</h2>
                            <p class="section-description">Set your branch operating hours for each day of the week</p>
                            
                            <div class="working-hours-grid">
                                <?php 
                                $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                                $dayLabels = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                                
                                foreach ($days as $index => $day): 
                                    $dayData = $formData['working_hours'][$day] ?? ['open' => '09:00', 'close' => '18:00', 'closed' => false];
                                ?>
                                    <div class="working-hours-day">
                                        <div class="day-header">
                                            <label class="day-label"><?php echo $dayLabels[$index]; ?></label>
                                            <div class="closed-toggle">
                                                <input type="checkbox" id="<?php echo $day; ?>_closed" name="<?php echo $day; ?>_closed" 
                                                       <?php echo $dayData['closed'] ? 'checked' : ''; ?>>
                                                <label for="<?php echo $day; ?>_closed">Closed</label>
                                            </div>
                                        </div>
                                        
                                        <div class="time-inputs" <?php echo $dayData['closed'] ? 'style="display: none;"' : ''; ?>>
                                            <div class="time-group">
                                                <label for="<?php echo $day; ?>_open">Open</label>
                                                <input type="time" id="<?php echo $day; ?>_open" name="<?php echo $day; ?>_open" 
                                                       value="<?php echo htmlspecialchars($dayData['open']); ?>">
                                            </div>
                                            <div class="time-group">
                                                <label for="<?php echo $day; ?>_close">Close</label>
                                                <input type="time" id="<?php echo $day; ?>_close" name="<?php echo $day; ?>_close" 
                                                       value="<?php echo htmlspecialchars($dayData['close']); ?>">
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Terms and Submit -->
                        <div class="form-section">
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="terms" name="terms" required>
                                    <label for="terms">
                                        I confirm that all information provided is accurate and I agree to the 
                                        <a href="#" target="_blank">Terms of Service</a> and 
                                        <a href="#" target="_blank">Business Agreement</a> *
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary btn-full">Submit Business Registration</button>
                            </div>

                            <div class="form-links">
                                <p><a href="/dashboard/shop-owner.php">← Back to Dashboard</a></p>
                            </div>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        // Working hours toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const closedCheckboxes = document.querySelectorAll('input[type="checkbox"][name$="_closed"]');
            
            closedCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const dayContainer = this.closest('.working-hours-day');
                    const timeInputs = dayContainer.querySelector('.time-inputs');
                    
                    if (this.checked) {
                        timeInputs.style.display = 'none';
                        // Clear time values when closed
                        const timeFields = timeInputs.querySelectorAll('input[type="time"]');
                        timeFields.forEach(field => field.removeAttribute('required'));
                    } else {
                        timeInputs.style.display = 'block';
                        // Make time fields required when open
                        const timeFields = timeInputs.querySelectorAll('input[type="time"]');
                        timeFields.forEach(field => field.setAttribute('required', 'required'));
                    }
                });
                
                // Trigger change event on page load to set initial state
                checkbox.dispatchEvent(new Event('change'));
            });
            
            // Form validation
            const form = document.querySelector('.shop-register-form');
            form.addEventListener('submit', function(e) {
                let isValid = true;
                
                // Check required fields
                const requiredFields = form.querySelectorAll('input[required], textarea[required]');
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('invalid');
                    } else {
                        field.classList.remove('invalid');
                    }
                });
                
                // Check working hours for open days
                const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                days.forEach(day => {
                    const closedCheckbox = document.getElementById(day + '_closed');
                    if (!closedCheckbox.checked) {
                        const openTime = document.getElementById(day + '_open');
                        const closeTime = document.getElementById(day + '_close');
                        
                        if (!openTime.value || !closeTime.value) {
                            isValid = false;
                            openTime.classList.add('invalid');
                            closeTime.classList.add('invalid');
                        } else if (openTime.value >= closeTime.value) {
                            isValid = false;
                            openTime.classList.add('invalid');
                            closeTime.classList.add('invalid');
                            alert('Opening time must be before closing time for ' + day.charAt(0).toUpperCase() + day.slice(1));
                        }
                    }
                });
                
                // Check terms acceptance
                const termsCheckbox = document.getElementById('terms');
                if (!termsCheckbox.checked) {
                    isValid = false;
                    termsCheckbox.classList.add('invalid');
                }
                
                if (!isValid) {
                    e.preventDefault();
                    // Scroll to first error
                    const firstError = document.querySelector('.invalid');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstError.focus();
                    }
                }
            });
            
            // Remove invalid class on focus
            document.querySelectorAll('input, textarea').forEach(input => {
                input.addEventListener('focus', function() {
                    this.classList.remove('invalid');
                });
            });
        });
    </script>
</body>
</html>