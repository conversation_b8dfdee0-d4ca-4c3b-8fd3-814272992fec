/**
 * Main JavaScript for Beauty Platform
 * Handles form validation, UI interactions, and AJAX functionality
 */

// Global utility functions
const BeautyPlatform = {
    // Form validation utilities
    validation: {
        email: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        
        password: function(password) {
            // At least 8 characters, one uppercase, one lowercase, one number
            const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
            return passwordRegex.test(password);
        },
        
        phone: function(phone) {
            const cleanPhone = phone.replace(/[^0-9]/g, '');
            return cleanPhone.length >= 10 && cleanPhone.length <= 15;
        },
        
        name: function(name) {
            const nameRegex = /^[a-zA-Z\s\-']{2,50}$/;
            return nameRegex.test(name.trim());
        }
    },
    
    // UI utilities
    ui: {
        showFieldError: function(field, message) {
            field.classList.add('error');
            
            // Remove existing error message
            const existingError = field.parentNode.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }
            
            // Add new error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        },
        
        hideFieldError: function(field) {
            field.classList.remove('error');
            const errorMessage = field.parentNode.querySelector('.error-message');
            if (errorMessage) {
                errorMessage.remove();
            }
        },
        
        showAlert: function(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            // Insert at top of form or page
            const container = document.querySelector('.auth-card') || document.body;
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        },
        
        togglePassword: function(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleButton = passwordField.nextElementSibling;
            const showText = toggleButton.querySelector('.show-text');
            const hideText = toggleButton.querySelector('.hide-text');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                showText.style.display = 'none';
                hideText.style.display = 'inline';
            } else {
                passwordField.type = 'password';
                showText.style.display = 'inline';
                hideText.style.display = 'none';
            }
        }
    },
    
    // AJAX utilities
    ajax: {
        request: function(url, options = {}) {
            const defaults = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            const config = Object.assign(defaults, options);
            
            return fetch(url, config)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('AJAX request failed:', error);
                    throw error;
                });
        }
    }
};

// Authentication form handlers
document.addEventListener('DOMContentLoaded', function() {
    // Login form validation
    const loginForm = document.querySelector('form[action*="login.php"]');
    if (loginForm) {
        initializeLoginForm(loginForm);
    }
    
    // Registration form validation
    const registerForm = document.querySelector('form[action*="register.php"]');
    if (registerForm) {
        initializeRegisterForm(registerForm);
    }
    
    // Password reset form validation
    const resetForm = document.querySelector('form[action*="reset-password.php"]');
    if (resetForm) {
        initializeResetForm(resetForm);
    }
    
    // Forgot password form validation
    const forgotForm = document.querySelector('form[action*="forgot-password.php"]');
    if (forgotForm) {
        initializeForgotForm(forgotForm);
    }
});

// Login form initialization
function initializeLoginForm(form) {
    const emailField = form.querySelector('#email');
    const passwordField = form.querySelector('#password');
    
    // Real-time email validation
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const email = this.value.trim();
            
            if (email && !BeautyPlatform.validation.email(email)) {
                BeautyPlatform.ui.showFieldError(this, 'Please enter a valid email address');
            } else {
                BeautyPlatform.ui.hideFieldError(this);
            }
        });
    }
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        let hasErrors = false;
        
        // Validate email
        if (emailField) {
            const email = emailField.value.trim();
            if (!email) {
                BeautyPlatform.ui.showFieldError(emailField, 'Email is required');
                hasErrors = true;
            } else if (!BeautyPlatform.validation.email(email)) {
                BeautyPlatform.ui.showFieldError(emailField, 'Please enter a valid email address');
                hasErrors = true;
            } else {
                BeautyPlatform.ui.hideFieldError(emailField);
            }
        }
        
        // Validate password
        if (passwordField) {
            const password = passwordField.value;
            if (!password) {
                BeautyPlatform.ui.showFieldError(passwordField, 'Password is required');
                hasErrors = true;
            } else {
                BeautyPlatform.ui.hideFieldError(passwordField);
            }
        }
        
        if (hasErrors) {
            e.preventDefault();
        }
    });
}

// Registration form initialization
function initializeRegisterForm(form) {
    const emailField = form.querySelector('#email');
    const passwordField = form.querySelector('#password');
    const confirmPasswordField = form.querySelector('#confirm_password');
    const firstNameField = form.querySelector('#first_name');
    const lastNameField = form.querySelector('#last_name');
    const phoneField = form.querySelector('#phone');
    
    // Real-time validation
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const email = this.value.trim();
            if (email && !BeautyPlatform.validation.email(email)) {
                BeautyPlatform.ui.showFieldError(this, 'Please enter a valid email address');
            } else {
                BeautyPlatform.ui.hideFieldError(this);
            }
        });
    }
    
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            const password = this.value;
            if (password && !BeautyPlatform.validation.password(password)) {
                BeautyPlatform.ui.showFieldError(this, 'Password must be at least 8 characters with uppercase, lowercase, and number');
            } else {
                BeautyPlatform.ui.hideFieldError(this);
            }
            
            // Also validate confirm password if it has a value
            if (confirmPasswordField && confirmPasswordField.value) {
                validatePasswordMatch();
            }
        });
    }
    
    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    }
    
    function validatePasswordMatch() {
        const password = passwordField ? passwordField.value : '';
        const confirmPassword = confirmPasswordField.value;
        
        if (confirmPassword && password !== confirmPassword) {
            BeautyPlatform.ui.showFieldError(confirmPasswordField, 'Passwords do not match');
        } else {
            BeautyPlatform.ui.hideFieldError(confirmPasswordField);
        }
    }
    
    if (firstNameField) {
        firstNameField.addEventListener('blur', function() {
            const name = this.value.trim();
            if (name && !BeautyPlatform.validation.name(name)) {
                BeautyPlatform.ui.showFieldError(this, 'Please enter a valid first name');
            } else {
                BeautyPlatform.ui.hideFieldError(this);
            }
        });
    }
    
    if (lastNameField) {
        lastNameField.addEventListener('blur', function() {
            const name = this.value.trim();
            if (name && !BeautyPlatform.validation.name(name)) {
                BeautyPlatform.ui.showFieldError(this, 'Please enter a valid last name');
            } else {
                BeautyPlatform.ui.hideFieldError(this);
            }
        });
    }
    
    if (phoneField) {
        phoneField.addEventListener('blur', function() {
            const phone = this.value.trim();
            if (phone && !BeautyPlatform.validation.phone(phone)) {
                BeautyPlatform.ui.showFieldError(this, 'Please enter a valid phone number');
            } else {
                BeautyPlatform.ui.hideFieldError(this);
            }
        });
    }
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        let hasErrors = false;
        
        // Validate all fields
        const fields = [
            { field: emailField, validator: 'email', message: 'Please enter a valid email address', required: true },
            { field: passwordField, validator: 'password', message: 'Password must be at least 8 characters with uppercase, lowercase, and number', required: true },
            { field: firstNameField, validator: 'name', message: 'Please enter a valid first name', required: true },
            { field: lastNameField, validator: 'name', message: 'Please enter a valid last name', required: true },
            { field: phoneField, validator: 'phone', message: 'Please enter a valid phone number', required: false }
        ];
        
        fields.forEach(fieldConfig => {
            if (fieldConfig.field) {
                const value = fieldConfig.field.value.trim();
                
                if (fieldConfig.required && !value) {
                    BeautyPlatform.ui.showFieldError(fieldConfig.field, fieldConfig.message.replace('valid', 'required'));
                    hasErrors = true;
                } else if (value && !BeautyPlatform.validation[fieldConfig.validator](value)) {
                    BeautyPlatform.ui.showFieldError(fieldConfig.field, fieldConfig.message);
                    hasErrors = true;
                } else {
                    BeautyPlatform.ui.hideFieldError(fieldConfig.field);
                }
            }
        });
        
        // Validate password confirmation
        if (confirmPasswordField) {
            const password = passwordField ? passwordField.value : '';
            const confirmPassword = confirmPasswordField.value;
            
            if (!confirmPassword) {
                BeautyPlatform.ui.showFieldError(confirmPasswordField, 'Password confirmation is required');
                hasErrors = true;
            } else if (password !== confirmPassword) {
                BeautyPlatform.ui.showFieldError(confirmPasswordField, 'Passwords do not match');
                hasErrors = true;
            } else {
                BeautyPlatform.ui.hideFieldError(confirmPasswordField);
            }
        }
        
        if (hasErrors) {
            e.preventDefault();
        }
    });
}

// Password reset form initialization
function initializeResetForm(form) {
    const passwordField = form.querySelector('#password');
    const confirmPasswordField = form.querySelector('#confirm_password');
    
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            const password = this.value;
            if (password && !BeautyPlatform.validation.password(password)) {
                BeautyPlatform.ui.showFieldError(this, 'Password must be at least 8 characters with uppercase, lowercase, and number');
            } else {
                BeautyPlatform.ui.hideFieldError(this);
            }
            
            // Also check confirm password if it has a value
            if (confirmPasswordField && confirmPasswordField.value) {
                validatePasswordMatch();
            }
        });
    }
    
    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    }
    
    function validatePasswordMatch() {
        const password = passwordField ? passwordField.value : '';
        const confirmPassword = confirmPasswordField.value;
        
        if (confirmPassword && password !== confirmPassword) {
            BeautyPlatform.ui.showFieldError(confirmPasswordField, 'Passwords do not match');
        } else {
            BeautyPlatform.ui.hideFieldError(confirmPasswordField);
        }
    }
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        let hasErrors = false;
        
        // Validate password
        if (passwordField) {
            const password = passwordField.value;
            if (!password) {
                BeautyPlatform.ui.showFieldError(passwordField, 'Password is required');
                hasErrors = true;
            } else if (!BeautyPlatform.validation.password(password)) {
                BeautyPlatform.ui.showFieldError(passwordField, 'Password must be at least 8 characters with uppercase, lowercase, and number');
                hasErrors = true;
            } else {
                BeautyPlatform.ui.hideFieldError(passwordField);
            }
        }
        
        // Validate password confirmation
        if (confirmPasswordField) {
            const password = passwordField ? passwordField.value : '';
            const confirmPassword = confirmPasswordField.value;
            
            if (!confirmPassword) {
                BeautyPlatform.ui.showFieldError(confirmPasswordField, 'Password confirmation is required');
                hasErrors = true;
            } else if (password !== confirmPassword) {
                BeautyPlatform.ui.showFieldError(confirmPasswordField, 'Passwords do not match');
                hasErrors = true;
            } else {
                BeautyPlatform.ui.hideFieldError(confirmPasswordField);
            }
        }
        
        if (hasErrors) {
            e.preventDefault();
        }
    });
}

// Forgot password form initialization
function initializeForgotForm(form) {
    const emailField = form.querySelector('#email');
    
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const email = this.value.trim();
            if (email && !BeautyPlatform.validation.email(email)) {
                BeautyPlatform.ui.showFieldError(this, 'Please enter a valid email address');
            } else {
                BeautyPlatform.ui.hideFieldError(this);
            }
        });
    }
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        if (emailField) {
            const email = emailField.value.trim();
            
            if (!email) {
                BeautyPlatform.ui.showFieldError(emailField, 'Email is required');
                e.preventDefault();
            } else if (!BeautyPlatform.validation.email(email)) {
                BeautyPlatform.ui.showFieldError(emailField, 'Please enter a valid email address');
                e.preventDefault();
            } else {
                BeautyPlatform.ui.hideFieldError(emailField);
            }
        }
    });
}

// Global password toggle function (called from HTML)
function togglePassword(fieldId) {
    BeautyPlatform.ui.togglePassword(fieldId);
}

// Homepage search functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize homepage search if on homepage
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        initializeHomepageSearch(searchForm);
    }
});

// Homepage search initialization
function initializeHomepageSearch(form) {
    const cityField = form.querySelector('#city');
    const countryField = form.querySelector('#country');
    
    // Add search validation
    form.addEventListener('submit', function(e) {
        const city = cityField ? cityField.value.trim() : '';
        const country = countryField ? countryField.value.trim() : '';
        
        // Require at least one search field
        if (!city && !country) {
            e.preventDefault();
            BeautyPlatform.ui.showAlert('Please enter a city or country to search', 'error');
            return;
        }
        
        // Basic validation for search terms
        if (city && city.length < 2) {
            e.preventDefault();
            BeautyPlatform.ui.showFieldError(cityField, 'City name must be at least 2 characters');
            return;
        }
        
        if (country && country.length < 2) {
            e.preventDefault();
            BeautyPlatform.ui.showFieldError(countryField, 'Country name must be at least 2 characters');
            return;
        }
        
        // Clear any existing errors
        if (cityField) BeautyPlatform.ui.hideFieldError(cityField);
        if (countryField) BeautyPlatform.ui.hideFieldError(countryField);
    });
    
    // Clear errors on input
    if (cityField) {
        cityField.addEventListener('input', function() {
            BeautyPlatform.ui.hideFieldError(this);
        });
    }
    
    if (countryField) {
        countryField.addEventListener('input', function() {
            BeautyPlatform.ui.hideFieldError(this);
        });
    }
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Focus search on Ctrl+K or Cmd+K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (cityField) {
                cityField.focus();
            }
        }
    });
}

// Booking system functionality
const BookingSystem = {
    // Initialize booking form
    initializeBookingForm: function() {
        const bookingForm = document.getElementById('bookingForm');
        if (!bookingForm) return;
        
        const dateField = document.getElementById('booking_date');
        const submitBtn = document.getElementById('submitBtn');
        
        // Initialize form state
        this.updateSubmitButton();
        
        // Set up event listeners
        if (dateField) {
            dateField.addEventListener('change', () => {
                this.loadAvailableSlots();
            });
        }
        
        // Form validation
        bookingForm.addEventListener('submit', (e) => {
            if (!this.validateBookingForm()) {
                e.preventDefault();
            }
        });
    },
    
    // Load available time slots for selected date
    loadAvailableSlots: function() {
        const dateField = document.getElementById('booking_date');
        const staffField = document.getElementById('staff_id');
        const timeSlotsContainer = document.getElementById('time-slots-container');
        
        if (!dateField || !timeSlotsContainer || !dateField.value) {
            return;
        }
        
        const selectedDate = dateField.value;
        const selectedStaff = staffField ? staffField.value : null;
        
        // Show loading state
        timeSlotsContainer.innerHTML = '<p class="loading">Loading available times...</p>';
        
        // Prepare request data
        const requestData = {
            service_id: serviceData.id,
            date: selectedDate
        };
        
        if (selectedStaff) {
            requestData.staff_id = selectedStaff;
        }
        
        // Make API request
        BeautyPlatform.ajax.request('api/availability.php', {
            method: 'POST',
            body: JSON.stringify(requestData)
        })
        .then(response => {
            this.displayTimeSlots(response.available_slots, response.message);
            this.updateSubmitButton();
        })
        .catch(error => {
            console.error('Failed to load time slots:', error);
            timeSlotsContainer.innerHTML = '<p class="error">Failed to load available times. Please try again.</p>';
        });
    },
    
    // Display available time slots
    displayTimeSlots: function(slots, message) {
        const timeSlotsContainer = document.getElementById('time-slots-container');
        
        if (!slots || slots.length === 0) {
            timeSlotsContainer.innerHTML = `<p class="no-slots">${message || 'No available time slots for this date'}</p>`;
            return;
        }
        
        // Create time slots grid
        let slotsHtml = '<div class="time-slots-grid">';
        
        slots.forEach(slot => {
            slotsHtml += `
                <label class="time-slot">
                    <input type="radio" name="booking_time" value="${slot.time}" required>
                    <span class="time-slot-label">${slot.formatted_time}</span>
                </label>
            `;
        });
        
        slotsHtml += '</div>';
        timeSlotsContainer.innerHTML = slotsHtml;
        
        // Add event listeners to time slots
        const timeSlotInputs = timeSlotsContainer.querySelectorAll('input[name="booking_time"]');
        timeSlotInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateSubmitButton();
            });
        });
    },
    
    // Update submit button state
    updateSubmitButton: function() {
        const submitBtn = document.getElementById('submitBtn');
        const dateField = document.getElementById('booking_date');
        const timeSlots = document.querySelectorAll('input[name="booking_time"]');
        
        if (!submitBtn) return;
        
        const hasDate = dateField && dateField.value;
        const hasSelectedTime = Array.from(timeSlots).some(slot => slot.checked);
        
        if (hasDate && hasSelectedTime) {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Book Appointment';
        } else {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Select Date & Time';
        }
    },
    
    // Validate booking form
    validateBookingForm: function() {
        let isValid = true;
        
        // Validate date
        const dateField = document.getElementById('booking_date');
        if (dateField) {
            const selectedDate = dateField.value;
            if (!selectedDate) {
                BeautyPlatform.ui.showFieldError(dateField, 'Please select a booking date');
                isValid = false;
            } else if (new Date(selectedDate) < new Date().setHours(0,0,0,0)) {
                BeautyPlatform.ui.showFieldError(dateField, 'Booking date cannot be in the past');
                isValid = false;
            } else {
                BeautyPlatform.ui.hideFieldError(dateField);
            }
        }
        
        // Validate time
        const timeSlots = document.querySelectorAll('input[name="booking_time"]');
        const hasSelectedTime = Array.from(timeSlots).some(slot => slot.checked);
        
        if (!hasSelectedTime) {
            const timeSlotsContainer = document.getElementById('time-slots-container');
            if (timeSlotsContainer) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = 'Please select a time slot';
                timeSlotsContainer.appendChild(errorDiv);
            }
            isValid = false;
        }
        
        // Validate notes length
        const notesField = document.getElementById('notes');
        if (notesField && notesField.value.length > 500) {
            BeautyPlatform.ui.showFieldError(notesField, 'Notes must be less than 500 characters');
            isValid = false;
        }
        
        return isValid;
    }
};

// Shop profile functionality
const ShopProfile = {
    // Initialize shop profile page
    init: function() {
        this.initializeTabs();
        this.initializeBookingButtons();
    },
    
    // Initialize tab functionality
    initializeTabs: function() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('onclick').match(/'([^']+)'/)[1];
                this.showTab(targetTab);
            });
        });
    },
    
    // Show specific tab
    showTab: function(tabName) {
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
        });
        
        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(button => {
            button.classList.remove('active');
        });
        
        // Show selected tab content
        const selectedTab = document.getElementById(tabName);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }
        
        // Add active class to selected tab button
        const selectedButton = Array.from(tabButtons).find(button => 
            button.getAttribute('onclick') && button.getAttribute('onclick').includes(tabName)
        );
        if (selectedButton) {
            selectedButton.classList.add('active');
        }
    },
    
    // Initialize booking buttons
    initializeBookingButtons: function() {
        const bookingButtons = document.querySelectorAll('[onclick^="bookService"]');
        bookingButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const serviceId = button.getAttribute('onclick').match(/\d+/)[0];
                this.bookService(serviceId);
            });
        });
    },
    
    // Handle service booking
    bookService: function(serviceId) {
        window.location.href = `/booking.php?service=${serviceId}`;
    },
    
    // Switch branch (for multi-branch shops)
    switchBranch: function(branchId) {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('branch', branchId);
        window.location.href = currentUrl.toString();
    }
};

// Global functions (called from HTML)
function initializeBookingForm() {
    BookingSystem.initializeBookingForm();
}

function loadAvailableSlots() {
    BookingSystem.loadAvailableSlots();
}

function showTab(tabName) {
    ShopProfile.showTab(tabName);
}

function bookService(serviceId) {
    ShopProfile.bookService(serviceId);
}

function switchBranch(branchId) {
    ShopProfile.switchBranch(branchId);
}

// Initialize components on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize shop profile if on shop page
    if (document.querySelector('.shop-header')) {
        ShopProfile.init();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { BeautyPlatform, BookingSystem, ShopProfile };
}
/
/ Customer Dashboard Functions
const CustomerDashboard = {
    // Cancel appointment modal functions
    showCancelModal: function(appointmentId, serviceName, shopName) {
        document.getElementById('cancelAppointmentId').value = appointmentId;
        document.getElementById('cancelServiceName').textContent = serviceName;
        document.getElementById('cancelShopName').textContent = shopName;
        document.getElementById('cancelModal').style.display = 'flex';
        
        // Focus on the reason textarea
        setTimeout(() => {
            document.getElementById('cancellation_reason').focus();
        }, 100);
    },
    
    hideCancelModal: function() {
        document.getElementById('cancelModal').style.display = 'none';
        document.getElementById('cancelForm').reset();
    },
    
    // Initialize dashboard functionality
    init: function() {
        // Close modal when clicking outside
        const modal = document.getElementById('cancelModal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    CustomerDashboard.hideCancelModal();
                }
            });
        }
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('cancelModal');
                if (modal && modal.style.display === 'flex') {
                    CustomerDashboard.hideCancelModal();
                }
            }
        });
        
        // Form validation for cancellation
        const cancelForm = document.getElementById('cancelForm');
        if (cancelForm) {
            cancelForm.addEventListener('submit', function(e) {
                const reason = document.getElementById('cancellation_reason').value.trim();
                
                // Optional validation - reason is not required but if provided should be reasonable length
                if (reason.length > 500) {
                    e.preventDefault();
                    alert('Cancellation reason must be less than 500 characters.');
                    return false;
                }
                
                // Confirm cancellation
                if (!confirm('Are you sure you want to cancel this appointment? This action cannot be undone.')) {
                    e.preventDefault();
                    return false;
                }
            });
        }
        
        // Auto-hide flash messages after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }, 5000);
        });
    }
};

// Global functions for backward compatibility
function showCancelModal(appointmentId, serviceName, shopName) {
    CustomerDashboard.showCancelModal(appointmentId, serviceName, shopName);
}

function hideCancelModal() {
    CustomerDashboard.hideCancelModal();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize customer dashboard if we're on that page
    if (document.body.classList.contains('customer-dashboard') || 
        document.getElementById('cancelModal')) {
        CustomerDashboard.init();
    }
});