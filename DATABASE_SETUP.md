# Beauty Platform Database Setup Guide

This guide explains how to set up, manage, and maintain the Beauty Platform database using the provided scripts.

## Overview

The database management system includes:

- **Installation Script** (`includes/install.php`) - Creates database and tables with sample data
- **Backup Script** (`includes/backup.php`) - Creates and restores database backups
- **Migration Script** (`includes/migrate.php`) - <PERSON>les schema updates and versioning
- **Web Manager** (`database_manager.php`) - Web interface for database operations

## Quick Start

### 1. Web Interface (Recommended)

1. Open your browser and navigate to `http://your-domain/database_manager.php`
2. Login with password: `admin123`
3. Follow the dashboard instructions to install the database
4. The system will guide you through the setup process

### 2. Command Line Interface

```bash
# Install database
php includes/install.php

# Create backup
php includes/backup.php create

# Run migrations
php includes/migrate.php migrate

# Check migration status
php includes/migrate.php status
```

## Database Installation

### Using Web Interface

1. Access `database_manager.php`
2. Click "Install Database" from the dashboard
3. The system will create all tables and insert sample data

### Using Command Line

```bash
php includes/install.php
```

### What Gets Installed

- All database tables (users, shops, services, appointments, etc.)
- Sample data for testing:
  - Admin user (<EMAIL> / admin123)
  - Sample customers and shop owners
  - Example shops and services
  - Test appointments and reviews

## Database Backup & Recovery

### Creating Backups

#### Web Interface

1. Go to "Backup" section in database manager
2. Click "Create New Backup"
3. Backup files are stored in `/backups/` directory

#### Command Line

```bash
# Create backup with auto-generated filename
php includes/backup.php create

# Create backup with custom filename
php includes/backup.php create my_backup.sql

# Create automated backup (with cleanup)
php includes/backup.php create
```

### Restoring Backups

#### Web Interface

1. Go to "Backup" section
2. Find the backup file in the list
3. Click "Restore" (⚠️ This will overwrite current data!)

#### Command Line

```bash
# Restore from backup file
php includes/backup.php restore backup_2024-01-15_10-30-00.sql

# List available backups
php includes/backup.php list

# Show backup statistics
php includes/backup.php stats

# Cleanup old backups (keep 10 most recent)
php includes/backup.php cleanup 10
```

## Database Migrations

Migrations handle database schema updates and versioning.

### Running Migrations

#### Web Interface

1. Go to "Migrate" section
2. View pending migrations
3. Click "Run Pending Migrations"

#### Command Line

```bash
# Run all pending migrations
php includes/migrate.php migrate

# Check migration status
php includes/migrate.php status

# Rollback last migration
php includes/migrate.php rollback

# Rollback multiple migrations
php includes/migrate.php rollback 3
```

### Available Migrations

1. **001_initial_schema** - Creates all base tables
2. **002_add_indexes** - Adds performance indexes
3. **003_add_admin_logs** - Adds admin activity logging
4. **004_add_password_reset** - Adds password reset functionality
5. **005_add_sessions_table** - Adds session management

## Configuration

### Database Settings

Edit `includes/config.php` to configure database connection:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'beauty_platform');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');
```

### Security Settings

- Change default admin password in production
- Update session security settings
- Configure proper file permissions

## File Structure

```
/
├── includes/
│   ├── config.php          # Database configuration
│   ├── database.php        # Database connection class
│   ├── schema.sql          # Database schema definition
│   ├── install.php         # Installation script
│   ├── backup.php          # Backup/restore script
│   └── migrate.php         # Migration script
├── backups/                # Backup files directory
├── database_manager.php    # Web management interface
└── DATABASE_SETUP.md       # This documentation
```

## Sample Data

The installation includes sample data for testing:

### Users

- **Admin**: <EMAIL> / admin123
- **Customer**: <EMAIL> / customer123
- **Customer**: <EMAIL> / customer123
- **Shop Owner**: <EMAIL> / owner123
- **Shop Owner**: <EMAIL> / owner123

### Shops

- **Elegant Hair Salon** - Hair cutting and coloring services
- **Serenity Spa & Beauty** - Spa and wellness services

### Services

- Women's Haircut & Style ($75, 60 min)
- Hair Coloring ($150, 120 min)
- Men's Haircut ($45, 30 min)
- Relaxing Facial ($120, 90 min)
- Swedish Massage ($100, 60 min)
- Manicure & Pedicure ($65, 75 min)

## Troubleshooting

### Common Issues

#### Database Connection Failed

- Check database credentials in `includes/config.php`
- Ensure MySQL server is running
- Verify database user has proper permissions

#### Installation Fails

- Check PHP error logs
- Ensure write permissions on `/backups/` directory
- Verify MySQL user has CREATE/DROP privileges

#### Backup/Restore Issues

- Check file permissions on `/backups/` directory
- Ensure sufficient disk space
- Verify MySQL user has SELECT/INSERT/UPDATE/DELETE privileges

#### Migration Errors

- Check migration status: `php includes/migrate.php status`
- Review error logs for specific issues
- Ensure database schema is compatible

### Error Logs

- PHP errors: Check server error logs
- Application errors: Check `/logs/` directory (if configured)
- Database errors: Enable MySQL query logging

## Maintenance

### Regular Tasks

1. **Create backups regularly** (daily/weekly depending on usage)
2. **Clean up old backups** to save disk space
3. **Monitor database size** and performance
4. **Update passwords** and security settings

### Automated Backup

Set up a cron job for automated backups:

```bash
# Daily backup at 2 AM
0 2 * * * /usr/bin/php /path/to/your/site/includes/backup.php create
```

### Performance Monitoring

- Monitor slow queries
- Check index usage
- Review table sizes and growth

## Security Considerations

### Production Deployment

1. **Change default passwords** immediately
2. **Enable HTTPS** for all database operations
3. **Restrict file permissions** (644 for PHP files, 755 for directories)
4. **Disable error display** in production
5. **Use strong database passwords**
6. **Limit database user privileges**

### Access Control

- Protect `database_manager.php` with proper authentication
- Restrict access to backup files
- Monitor admin activity logs

## Support

For issues or questions:

1. Check this documentation
2. Review error logs
3. Verify configuration settings
4. Test with sample data

Remember to always backup your database before making significant changes!
