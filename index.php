<?php 

/**
 * Homepage - Beauty Platform
 * Main landing page with search functionality for finding beauty shops
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/functions.php';

// Initialize database query helper
$db = new DatabaseQuery();

// Handle search parameters
$searchCity = isset($_GET['city']) ? sanitizeString($_GET['city']) : '';
$searchCountry = isset($_GET['country']) ? sanitizeString($_GET['country']) : '';
$searchPerformed = !empty($searchCity) || !empty($searchCountry);

// Initialize search results
$shops = [];
$totalResults = 0;

// Perform search if parameters provided
if ($searchPerformed) {
    try {
        // Build search conditions
        $conditions = [];
        $params = [];

        if (!empty($searchCity)) {
            $conditions[] = "sb.city LIKE :city";
            $params[':city'] = '%' . $searchCity . '%';
        }

        if (!empty($searchCountry)) {
            $conditions[] = "sb.country LIKE :country";
            $params[':country'] = '%' . $searchCountry . '%';
        }

        // Only show approved shops with active branches
        $conditions[] = "s.status = 'approved'";
        $conditions[] = "sb.status = 'active'";

        // Build the search query
        $whereClause = implode(' AND ', $conditions);

        $searchQuery = "
            SELECT DISTINCT
                s.id as shop_id,
                s.business_name,
                s.description,
                sb.id as branch_id,
                sb.branch_name,
                sb.city,
                sb.country,
                sb.address,
                sb.phone,
                COALESCE(AVG(r.rating), 0) as average_rating,
                COUNT(r.id) as review_count,
                (SELECT COUNT(*) FROM services srv WHERE srv.branch_id = sb.id AND srv.status = 'active') as service_count
            FROM shops s
            INNER JOIN shop_branches sb ON s.id = sb.shop_id
            LEFT JOIN reviews r ON s.id = r.shop_id AND r.status = 'active'
            WHERE {$whereClause}
            GROUP BY s.id, sb.id
            ORDER BY average_rating DESC, review_count DESC, s.business_name ASC
        ";

        $shops = $db->query($searchQuery, $params);
        $totalResults = count($shops);
    } catch (Exception $e) {
        logError("Search query failed: " . $e->getMessage(), [
            'city' => $searchCity,
            'country' => $searchCountry
        ]);
        setFlashMessage('error', 'Search temporarily unavailable. Please try again.');
    }
}

// Get popular cities for suggestions (top 10 by shop count)
try {
    $popularCities = $db->query("
        SELECT 
            sb.city,
            sb.country,
            COUNT(DISTINCT s.id) as shop_count
        FROM shop_branches sb
        INNER JOIN shops s ON sb.shop_id = s.id
        WHERE s.status = 'approved' AND sb.status = 'active'
        GROUP BY sb.city, sb.country
        ORDER BY shop_count DESC
        LIMIT 10
    ");
} catch (Exception $e) {
    $popularCities = [];
    logError("Failed to fetch popular cities: " . $e->getMessage());
}

// Get featured shops (highest rated with most reviews)
try {
    $featuredShops = $db->query("
        SELECT DISTINCT
            s.id as shop_id,
            s.business_name,
            s.description,
            sb.city,
            sb.country,
            COALESCE(AVG(r.rating), 0) as average_rating,
            COUNT(r.id) as review_count
        FROM shops s
        INNER JOIN shop_branches sb ON s.id = sb.shop_id
        LEFT JOIN reviews r ON s.id = r.shop_id AND r.status = 'active'
        WHERE s.status = 'approved' AND sb.status = 'active'
        GROUP BY s.id
        HAVING review_count >= 3 AND average_rating >= 4.0
        ORDER BY average_rating DESC, review_count DESC
        LIMIT 6
    ");
} catch (Exception $e) {
    $featuredShops = [];
    logError("Failed to fetch featured shops: " . $e->getMessage());
}

$pageTitle = 'Find Beauty & Hair Shops Near You';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="header-content">
                <div class="logo">
                    <h1><a href="index.php">Beauty Platform</a></h1>
                </div>
                <nav class="nav">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="dashboard/<?php echo $_SESSION['role']; ?>.php" class="nav-link">Dashboard</a>
                        <a href="logout.php" class="nav-link">Logout</a>
                    <?php else: ?>
                        <a href="login.php" class="nav-link">Login</a>
                        <a href="register.php" class="nav-link">Register</a>
                    <?php endif; ?>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="page-main">
        <!-- Hero Section with Search -->
        <section class="hero">
            <div class="hero-bg-illustration"></div>
            <div class="page-section">
                <div class="hero-content">
                    <h1 class="hero-title">Discover & Book Top Beauty Experiences</h1>
                    <p class="hero-subtitle">Find the best salons, spas, and beauty professionals near you.<br>Book instantly. Look and feel your best.</p>
                    <div class="hero-cta">
                        <a href="#popular-cities" class="btn btn-primary btn-lg">Browse Popular Locations</a>
                    </div>
                    <div class="hero-animated-icon">💅✨</div>
                    <!-- Search Form -->
                    <form class="search-form hero-search-form" method="GET" action="index.php">
                        <div class="search-fields">
                            <div class="search-field">
                                <label for="city" class="sr-only">City</label>
                                <input
                                    type="text"
                                    id="city"
                                    name="city"
                                    placeholder="Enter city..."
                                    value="<?php echo htmlspecialchars($searchCity); ?>"
                                    class="search-input">
                            </div>
                            <div class="search-field">
                                <label for="country" class="sr-only">Country</label>
                                <input
                                    type="text"
                                    id="country"
                                    name="country"
                                    placeholder="Enter country..."
                                    value="<?php echo htmlspecialchars($searchCountry); ?>"
                                    class="search-input">
                            </div>
                            <button type="submit" class="search-btn">
                                <span class="search-icon">🔍</span>
                                Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Flash Messages -->
        <?php
        $flashMessages = getFlashMessages();
        if (!empty($flashMessages)):
        ?>
            <div class="page-section">
                <div class="flash-messages">
                    <?php foreach ($flashMessages as $message): ?>
                        <div class="alert alert-<?php echo htmlspecialchars($message['type']); ?>">
                            <?php echo htmlspecialchars($message['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Search Results Section -->
        <?php if ($searchPerformed): ?>
            <section class="search-results">
                <div class="page-section">
                    <div class="results-header">
                        <h2>Search Results</h2>
                        <p class="results-count">
                            <?php if ($totalResults > 0): ?>
                                Found <?php echo $totalResults; ?> shop<?php echo $totalResults !== 1 ? 's' : ''; ?>
                                <?php if (!empty($searchCity) && !empty($searchCountry)): ?>
                                    in <?php echo htmlspecialchars($searchCity); ?>, <?php echo htmlspecialchars($searchCountry); ?>
                                <?php elseif (!empty($searchCity)): ?>
                                    in <?php echo htmlspecialchars($searchCity); ?>
                                <?php elseif (!empty($searchCountry)): ?>
                                    in <?php echo htmlspecialchars($searchCountry); ?>
                                <?php endif; ?>
                            <?php else: ?>
                                No shops found
                                <?php if (!empty($searchCity) || !empty($searchCountry)): ?>
                                    for your search criteria
                                <?php endif; ?>
                            <?php endif; ?>
                        </p>
                    </div>

                    <?php if ($totalResults > 0): ?>
                        <div class="shops-grid">
                            <?php foreach ($shops as $shop): ?>
                                <div class="shop-card">
                                    <div class="shop-header">
                                        <h3 class="shop-name">
                                            <a href="shop.php?id=<?php echo $shop['shop_id']; ?>&branch=<?php echo $shop['branch_id']; ?>">
                                                <?php echo htmlspecialchars($shop['business_name']); ?>
                                            </a>
                                        </h3>
                                        <?php if (!empty($shop['branch_name']) && $shop['branch_name'] !== $shop['business_name']): ?>
                                            <p class="branch-name"><?php echo htmlspecialchars($shop['branch_name']); ?></p>
                                        <?php endif; ?>
                                    </div>

                                    <div class="shop-location">
                                        <span class="location-icon">📍</span>
                                        <?php echo htmlspecialchars($shop['city']); ?>, <?php echo htmlspecialchars($shop['country']); ?>
                                    </div>

                                    <?php if (!empty($shop['description'])): ?>
                                        <p class="shop-description">
                                            <?php echo htmlspecialchars(substr($shop['description'], 0, 120)); ?>
                                            <?php if (strlen($shop['description']) > 120): ?>...<?php endif; ?>
                                        </p>
                                    <?php endif; ?>

                                    <div class="shop-stats">
                                        <div class="rating">
                                            <?php if ($shop['review_count'] > 0): ?>
                                                <span class="stars">
                                                    <?php
                                                    $rating = round($shop['average_rating']);
                                                    for ($i = 1; $i <= 5; $i++):
                                                        echo $i <= $rating ? '⭐' : '☆';
                                                    endfor;
                                                    ?>
                                                </span>
                                                <span class="rating-text">
                                                    <?php echo number_format($shop['average_rating'], 1); ?>
                                                    (<?php echo $shop['review_count']; ?> review<?php echo $shop['review_count'] !== 1 ? 's' : ''; ?>)
                                                </span>
                                            <?php else: ?>
                                                <span class="no-rating">No reviews yet</span>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($shop['service_count'] > 0): ?>
                                            <div class="services-count">
                                                <?php echo $shop['service_count']; ?> service<?php echo $shop['service_count'] !== 1 ? 's' : ''; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="shop-actions">
                                        <a href="shop.php?id=<?php echo $shop['shop_id']; ?>&branch=<?php echo $shop['branch_id']; ?>"
                                            class="btn btn-primary">View Details</a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <!-- No Results Found -->
                        <div class="no-results">
                            <div class="no-results-icon">🔍</div>
                            <h3>No shops found</h3>
                            <p>We couldn't find any beauty shops matching your search criteria.</p>

                            <div class="search-suggestions">
                                <h4>Try these suggestions:</h4>
                                <ul>
                                    <li>Check your spelling and try again</li>
                                    <li>Try searching with just the city name</li>
                                    <li>Use a broader location (e.g., country only)</li>
                                    <li>Browse our popular locations below</li>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </section>
        <?php endif; ?>

        <!-- Popular Cities Section -->
        <?php if (!empty($popularCities) && (!$searchPerformed || $totalResults === 0)): ?>
            <section class="popular-cities" id="popular-cities">
                <div class="page-section">
                    <h2>Popular Locations</h2>
                    <div class="cities-grid">
                        <?php foreach ($popularCities as $city): ?>
                            <a href="index.php?city=<?php echo urlencode($city['city']); ?>&country=<?php echo urlencode($city['country']); ?>"
                                class="city-card">
                                <h3><?php echo htmlspecialchars($city['city']); ?></h3>
                                <p><?php echo htmlspecialchars($city['country']); ?></p>
                                <span class="shop-count"><?php echo $city['shop_count']; ?> shop<?php echo $city['shop_count'] !== 1 ? 's' : ''; ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>

        <!-- Featured Shops Section -->
        <?php if (!empty($featuredShops) && !$searchPerformed): ?>
            <section class="featured-shops">
                <div class="page-section">
                    <h2>Top Rated Shops</h2>
                    <div class="shops-grid">
                        <?php foreach ($featuredShops as $shop): ?>
                            <div class="shop-card featured">
                                <div class="featured-badge">⭐ Featured</div>
                                <div class="shop-header">
                                    <h3 class="shop-name">
                                        <a href="shop.php?id=<?php echo $shop['shop_id']; ?>">
                                            <?php echo htmlspecialchars($shop['business_name']); ?>
                                        </a>
                                    </h3>
                                </div>

                                <div class="shop-location">
                                    <span class="location-icon">📍</span>
                                    <?php echo htmlspecialchars($shop['city']); ?>, <?php echo htmlspecialchars($shop['country']); ?>
                                </div>

                                <?php if (!empty($shop['description'])): ?>
                                    <p class="shop-description">
                                        <?php echo htmlspecialchars(substr($shop['description'], 0, 100)); ?>
                                        <?php if (strlen($shop['description']) > 100): ?>...<?php endif; ?>
                                    </p>
                                <?php endif; ?>

                                <div class="shop-stats">
                                    <div class="rating">
                                        <span class="stars">
                                            <?php
                                            $rating = round($shop['average_rating']);
                                            for ($i = 1; $i <= 5; $i++):
                                                echo $i <= $rating ? '⭐' : '☆';
                                            endfor;
                                            ?>
                                        </span>
                                        <span class="rating-text">
                                            <?php echo number_format($shop['average_rating'], 1); ?>
                                            (<?php echo $shop['review_count']; ?> review<?php echo $shop['review_count'] !== 1 ? 's' : ''; ?>)
                                        </span>
                                    </div>
                                </div>

                                <div class="shop-actions">
                                    <a href="shop.php?id=<?php echo $shop['shop_id']; ?>"
                                        class="btn btn-primary">View Shop</a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="page-section">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Beauty Platform</h3>
                    <p>Connecting you with the best beauty and hair professionals in your area.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="register.php">Join as Shop Owner</a></li>
                        <li><a href="login.php">Login</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#contact">Contact Us</a></li>
                        <li><a href="#help">Help Center</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> Beauty Platform. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
</body>

</html>