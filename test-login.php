<?php
/**
 * Simple test script to verify login functionality
 * This file can be removed after testing
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

echo "<h2>Authentication System Test</h2>";

// Test 1: Check if authentication system initializes
echo "<h3>Test 1: Authentication System Initialization</h3>";
try {
    $auth = new AuthSystem();
    echo "✓ Authentication system initialized successfully<br>";
} catch (Exception $e) {
    echo "✗ Failed to initialize authentication system: " . $e->getMessage() . "<br>";
}

// Test 2: Test password hashing
echo "<h3>Test 2: Password Hashing</h3>";
$testPassword = "TestPassword123";
$hashedPassword = $auth->hashPassword($testPassword);
if ($auth->verifyPassword($testPassword, $hashedPassword)) {
    echo "✓ Password hashing and verification working correctly<br>";
} else {
    echo "✗ Password hashing or verification failed<br>";
}

// Test 3: Test CSRF token generation
echo "<h3>Test 3: CSRF Token Generation</h3>";
$token1 = $auth->generateCSRFToken();
$token2 = $auth->generateCSRFToken();
if (!empty($token1) && $token1 === $token2) {
    echo "✓ CSRF token generation working correctly<br>";
} else {
    echo "✗ CSRF token generation failed<br>";
}

// Test 4: Test session management
echo "<h3>Test 4: Session Management</h3>";
if ($auth->initializeSession()) {
    echo "✓ Session initialization working correctly<br>";
} else {
    echo "✗ Session initialization failed<br>";
}

// Test 5: Test authentication state
echo "<h3>Test 5: Authentication State</h3>";
if (!$auth->isAuthenticated()) {
    echo "✓ Correctly detecting unauthenticated state<br>";
} else {
    echo "✗ Authentication state detection failed<br>";
}

// Test 6: Test database connection
echo "<h3>Test 6: Database Connection</h3>";
try {
    $db = new DatabaseQuery();
    echo "✓ Database connection established successfully<br>";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test 7: Test validation functions
echo "<h3>Test 7: Validation Functions</h3>";
$testEmail = "<EMAIL>";
$testInvalidEmail = "invalid-email";
$testValidPassword = "ValidPass123";
$testInvalidPassword = "weak";

if (validateEmail($testEmail) && !validateEmail($testInvalidEmail)) {
    echo "✓ Email validation working correctly<br>";
} else {
    echo "✗ Email validation failed<br>";
}

if (validatePassword($testValidPassword) && !validatePassword($testInvalidPassword)) {
    echo "✓ Password validation working correctly<br>";
} else {
    echo "✗ Password validation failed<br>";
}

echo "<h3>Test Summary</h3>";
echo "All core authentication components have been tested. ";
echo "If all tests show ✓, the authentication system is ready for use.<br>";
echo "<br><strong>Next Steps:</strong><br>";
echo "1. Test the login form at <a href='login.php'>login.php</a><br>";
echo "2. Test the registration form at <a href='register.php'>register.php</a><br>";
echo "3. Test the password reset at <a href='forgot-password.php'>forgot-password.php</a><br>";
echo "4. Remove this test file when done testing<br>";
?>