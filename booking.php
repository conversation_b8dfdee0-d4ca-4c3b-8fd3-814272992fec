<?php
/**
 * Appointment Booking System - Beauty Platform
 * Handles service selection, time slot display, and booking confirmation
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Require customer authentication
requireRole('customer', '/login.php');

// Initialize database query helper
$db = new DatabaseQuery();

// Get service ID from URL parameter
$serviceId = isset($_GET['service']) ? sanitizeInt($_GET['service']) : null;

// Validate service ID 
if (!$serviceId || !validateId($serviceId)) {
    setFlashMessage('error', 'Invalid service selected.');
    redirect('/index.php');
}

try {
    // Get service information with branch and shop details
    $service = $db->queryOne("
        SELECT 
            s.*,
            sb.branch_name,
            sb.address,
            sb.city,
            sb.country,
            sb.phone,
            sb.working_hours,
            sh.business_name,
            sh.id as shop_id
        FROM services s
        INNER JOIN shop_branches sb ON s.branch_id = sb.id
        INNER JOIN shops sh ON sb.shop_id = sh.id
        WHERE s.id = :service_id 
        AND s.status = 'active' 
        AND sb.status = 'active' 
        AND sh.status = 'approved'
    ", [':service_id' => $serviceId]);

    if (!$service) {
        setFlashMessage('error', 'Service not found or not available.');
        redirect('/index.php');
    }

    // Get staff members for this branch
    $staff = $db->select(
        'staff',
        ['branch_id' => $service['branch_id'], 'status' => 'active'],
        [],
        'name ASC'
    );

    // Parse working hours
    $workingHours = parseWorkingHours($service['working_hours']);

    // Handle booking form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $bookingResult = handleBookingSubmission($db, $service, $_POST);
        
        if ($bookingResult['success']) {
            setFlashMessage('success', $bookingResult['message']);
            redirect('/dashboard/customer.php');
        } else {
            $errors = $bookingResult['errors'];
        }
    }

} catch (Exception $e) {
    logError("Failed to load booking page: " . $e->getMessage(), [
        'service_id' => $serviceId,
        'user_id' => getCurrentUserId()
    ]);
    setFlashMessage('error', 'Unable to load booking page. Please try again.');
    redirect('/index.php');
}

/**
 * Handle booking form submission
 */
function handleBookingSubmission($db, $service, $postData) {
    try {
        // Validate CSRF token
        if (!isset($postData['csrf_token']) || !verifyCsrfToken($postData['csrf_token'])) {
            return [
                'success' => false,
                'errors' => ['general' => 'Invalid form submission. Please try again.']
            ];
        }

        // Validate booking data
        $validationResult = validateBookingData($postData);
        if (!$validationResult['valid']) {
            return [
                'success' => false,
                'errors' => $validationResult['errors']
            ];
        }

        $bookingDate = $postData['booking_date'];
        $bookingTime = $postData['booking_time'];
        $staffId = !empty($postData['staff_id']) ? (int)$postData['staff_id'] : null;
        $notes = !empty($postData['notes']) ? sanitizeText($postData['notes']) : null;

        // Check availability
        $availabilityResult = checkTimeSlotAvailability($db, $service, $bookingDate, $bookingTime, $staffId);
        if (!$availabilityResult['available']) {
            return [
                'success' => false,
                'errors' => ['time_slot' => $availabilityResult['message']]
            ];
        }

        // Begin transaction
        $db->beginTransaction();

        // Create appointment
        $appointmentData = [
            'customer_id' => getCurrentUserId(),
            'service_id' => $service['id'],
            'staff_id' => $staffId,
            'appointment_date' => $bookingDate,
            'appointment_time' => $bookingTime,
            'duration_minutes' => $service['duration_minutes'],
            'total_price' => $service['price'],
            'status' => 'pending',
            'notes' => $notes,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $appointmentId = $db->insert('appointments', $appointmentData);

        if (!$appointmentId) {
            $db->rollback();
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to create appointment. Please try again.']
            ];
        }

        // Commit transaction
        $db->commit();

        // Log booking
        logSecurityEvent('appointment_booked', [
            'appointment_id' => $appointmentId,
            'customer_id' => getCurrentUserId(),
            'service_id' => $service['id'],
            'booking_date' => $bookingDate,
            'booking_time' => $bookingTime
        ]);

        return [
            'success' => true,
            'appointment_id' => $appointmentId,
            'message' => 'Appointment booked successfully! You will receive a confirmation shortly.'
        ];

    } catch (Exception $e) {
        if (isset($db)) {
            $db->rollback();
        }
        logError('Booking submission error: ' . $e->getMessage(), $postData);
        return [
            'success' => false,
            'errors' => ['general' => 'Booking failed. Please try again.']
        ];
    }
}

/**
 * Validate booking form data
 */
function validateBookingData($data) {
    $errors = [];

    // Validate booking date
    if (empty($data['booking_date'])) {
        $errors['booking_date'] = 'Please select a booking date';
    } elseif (!validateDate($data['booking_date'])) {
        $errors['booking_date'] = 'Please enter a valid date';
    } elseif (strtotime($data['booking_date']) < strtotime(date('Y-m-d'))) {
        $errors['booking_date'] = 'Booking date cannot be in the past';
    }

    // Validate booking time
    if (empty($data['booking_time'])) {
        $errors['booking_time'] = 'Please select a booking time';
    } elseif (!validateTime($data['booking_time'])) {
        $errors['booking_time'] = 'Please enter a valid time';
    }

    // Validate staff ID (optional)
    if (!empty($data['staff_id']) && !validateId($data['staff_id'])) {
        $errors['staff_id'] = 'Invalid staff member selected';
    }

    // Validate notes (optional)
    if (!empty($data['notes']) && strlen($data['notes']) > 500) {
        $errors['notes'] = 'Notes must be less than 500 characters';
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * Check if time slot is available
 */
function checkTimeSlotAvailability($db, $service, $date, $time, $staffId = null) {
    try {
        // Check if the shop is open at this time
        $workingHours = parseWorkingHours($service['working_hours']);
        $dayOfWeek = strtolower(date('l', strtotime($date)));
        
        if (!isShopOpen($service['working_hours'], $dayOfWeek, $time)) {
            return [
                'available' => false,
                'message' => 'Shop is closed at the selected time'
            ];
        }

        // Check for existing appointments at this time
        $conditions = [
            'appointment_date' => $date,
            'appointment_time' => $time,
            'status' => 'confirmed'
        ];

        if ($staffId) {
            $conditions['staff_id'] = $staffId;
        } else {
            // Check if any staff member is available for this service
            $conditions['service_id'] = $service['id'];
        }

        $existingAppointment = $db->selectOne('appointments', $conditions);

        if ($existingAppointment) {
            return [
                'available' => false,
                'message' => 'This time slot is already booked'
            ];
        }

        return [
            'available' => true,
            'message' => 'Time slot is available'
        ];

    } catch (Exception $e) {
        logError('Availability check error: ' . $e->getMessage(), [
            'service_id' => $service['id'],
            'date' => $date,
            'time' => $time,
            'staff_id' => $staffId
        ]);
        return [
            'available' => false,
            'message' => 'Unable to check availability'
        ];
    }
}

$pageTitle = 'Book Appointment - ' . htmlspecialchars($service['business_name']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="header-content">
                <div class="logo">
                    <h1><a href="index.php">Beauty Platform</a></h1>
                </div>
                <nav class="nav">
                    <a href="dashboard/customer.php" class="nav-link">Dashboard</a>
                    <a href="logout.php" class="nav-link">Logout</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Breadcrumb -->
        <div class="container">
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="shop.php?id=<?php echo $service['shop_id']; ?>">
                    <?php echo htmlspecialchars($service['business_name']); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current">Book Appointment</span>
            </nav>
        </div>

        <!-- Flash Messages -->
        <?php
        $flashMessages = getFlashMessages();
        if (!empty($flashMessages)):
        ?>
            <div class="container">
                <?php foreach ($flashMessages as $message): ?>
                    <div class="alert alert-<?php echo $message['type']; ?>">
                        <?php echo htmlspecialchars($message['message']); ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Booking Section -->
        <section class="booking-section">
            <div class="container">
                <div class="booking-container">
                    <!-- Service Information -->
                    <div class="service-info-card">
                        <h2>Service Details</h2>
                        <div class="service-details">
                            <h3><?php echo htmlspecialchars($service['name']); ?></h3>
                            <div class="shop-info">
                                <h4><?php echo htmlspecialchars($service['business_name']); ?></h4>
                                <p class="branch-name"><?php echo htmlspecialchars($service['branch_name']); ?></p>
                                <p class="address">
                                    <?php echo htmlspecialchars($service['address']); ?><br>
                                    <?php echo htmlspecialchars($service['city']); ?>, <?php echo htmlspecialchars($service['country']); ?>
                                </p>
                                <?php if (!empty($service['phone'])): ?>
                                    <p class="phone">📞 <?php echo htmlspecialchars($service['phone']); ?></p>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (!empty($service['description'])): ?>
                                <div class="service-description">
                                    <p><?php echo htmlspecialchars($service['description']); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="service-meta">
                                <div class="duration">
                                    <span class="icon">⏱️</span>
                                    <span><?php echo $service['duration_minutes']; ?> minutes</span>
                                </div>
                                <div class="price">
                                    <span class="icon">💰</span>
                                    <span><?php echo formatPrice($service['price']); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Form -->
                    <div class="booking-form-card">
                        <h2>Select Date & Time</h2>
                        
                        <form method="POST" class="booking-form" id="bookingForm">
                            <input type="hidden" name="csrf_token" value="<?php echo csrfToken(); ?>">
                            
                            <!-- Date Selection -->
                            <div class="form-group">
                                <label for="booking_date">Select Date *</label>
                                <input type="date" 
                                       id="booking_date" 
                                       name="booking_date" 
                                       min="<?php echo date('Y-m-d'); ?>"
                                       max="<?php echo date('Y-m-d', strtotime('+3 months')); ?>"
                                       value="<?php echo isset($_POST['booking_date']) ? htmlspecialchars($_POST['booking_date']) : ''; ?>"
                                       required
                                       onchange="loadAvailableSlots()">
                                <?php if (isset($errors['booking_date'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['booking_date']); ?></div>
                                <?php endif; ?>
                            </div>

                            <!-- Time Selection -->
                            <div class="form-group">
                                <label for="booking_time">Select Time *</label>
                                <div id="time-slots-container">
                                    <p class="time-slots-placeholder">Please select a date first</p>
                                </div>
                                <?php if (isset($errors['booking_time'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['booking_time']); ?></div>
                                <?php endif; ?>
                                <?php if (isset($errors['time_slot'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['time_slot']); ?></div>
                                <?php endif; ?>
                            </div>

                            <!-- Staff Selection -->
                            <?php if (!empty($staff)): ?>
                                <div class="form-group">
                                    <label for="staff_id">Preferred Staff Member (Optional)</label>
                                    <select id="staff_id" name="staff_id" onchange="loadAvailableSlots()">
                                        <option value="">Any available staff member</option>
                                        <?php foreach ($staff as $member): ?>
                                            <option value="<?php echo $member['id']; ?>"
                                                <?php echo (isset($_POST['staff_id']) && $_POST['staff_id'] == $member['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($member['name']); ?>
                                                <?php
                                                $specialties = json_decode($member['specialties'], true);
                                                if (!empty($specialties) && is_array($specialties)):
                                                ?>
                                                    - <?php echo htmlspecialchars(implode(', ', $specialties)); ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if (isset($errors['staff_id'])): ?>
                                        <div class="error-message"><?php echo htmlspecialchars($errors['staff_id']); ?></div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <!-- Notes -->
                            <div class="form-group">
                                <label for="notes">Special Notes (Optional)</label>
                                <textarea id="notes" 
                                          name="notes" 
                                          rows="3" 
                                          maxlength="500"
                                          placeholder="Any special requests or notes for your appointment..."><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : ''; ?></textarea>
                                <div class="char-count">
                                    <span id="notes-count">0</span>/500 characters
                                </div>
                                <?php if (isset($errors['notes'])): ?>
                                    <div class="error-message"><?php echo htmlspecialchars($errors['notes']); ?></div>
                                <?php endif; ?>
                            </div>

                            <!-- General Errors -->
                            <?php if (isset($errors['general'])): ?>
                                <div class="alert alert-error">
                                    <?php echo htmlspecialchars($errors['general']); ?>
                                </div>
                            <?php endif; ?>

                            <!-- Submit Button -->
                            <div class="form-actions">
                                <button type="button" onclick="history.back()" class="btn btn-secondary">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                    Book Appointment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Working Hours Information -->
                <div class="working-hours-info">
                    <h3>Working Hours</h3>
                    <?php if (!empty($workingHours)): ?>
                        <div class="hours-grid">
                            <?php
                            $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                            $dayNames = [
                                'monday' => 'Monday',
                                'tuesday' => 'Tuesday',
                                'wednesday' => 'Wednesday',
                                'thursday' => 'Thursday',
                                'friday' => 'Friday',
                                'saturday' => 'Saturday',
                                'sunday' => 'Sunday'
                            ];

                            foreach ($days as $day):
                                $dayHours = $workingHours[$day] ?? null;
                            ?>
                                <div class="hours-item">
                                    <span class="day"><?php echo $dayNames[$day]; ?></span>
                                    <span class="hours">
                                        <?php if ($dayHours && isset($dayHours['open']) && isset($dayHours['close'])): ?>
                                            <?php echo formatTime($dayHours['open']); ?> - <?php echo formatTime($dayHours['close']); ?>
                                        <?php else: ?>
                                            Closed
                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p>Working hours information is not available.</p>
                    <?php endif; ?>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Beauty Platform. All rights reserved.</p>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script>
        // Service and working hours data for JavaScript
        const serviceData = {
            id: <?php echo $service['id']; ?>,
            duration: <?php echo $service['duration_minutes']; ?>,
            branchId: <?php echo $service['branch_id']; ?>
        };

        const workingHours = <?php echo json_encode($workingHours); ?>;

        // Initialize booking form
        document.addEventListener('DOMContentLoaded', function() {
            initializeBookingForm();
        });

        // Character count for notes
        const notesTextarea = document.getElementById('notes');
        const notesCount = document.getElementById('notes-count');
        
        if (notesTextarea && notesCount) {
            notesTextarea.addEventListener('input', function() {
                notesCount.textContent = this.value.length;
            });
            
            // Initialize count
            notesCount.textContent = notesTextarea.value.length;
        }
    </script>
</body>
</html>