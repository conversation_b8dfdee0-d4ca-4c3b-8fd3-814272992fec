<?php
/**
 * User Logout Page
 * Handles user logout with session cleanup and security logging
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('/login.php');
}
 
// Handle logout request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token for security
    if (isset($_POST['csrf_token']) && verifyCsrfToken($_POST['csrf_token'])) {
        // Perform logout
        $result = $auth->logoutUser();
        
        // Set success message
        setFlashMessage('success', 'You have been logged out successfully.');
        
        // Redirect to login page
        redirect('/login.php');
    } else {
        // Invalid CSRF token
        setFlashMessage('error', 'Invalid logout request.');
        redirect('/');
    }
} else {
    // GET request - show logout confirmation
    $currentUser = getCurrentUser();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logout - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Logout</h1>
                <p>Are you sure you want to sign out?</p>
            </div>
            
            <?php if ($currentUser): ?>
                <div class="user-info">
                    <p>You are currently signed in as:</p>
                    <strong><?php echo htmlspecialchars($currentUser['name']); ?></strong>
                    <small>(<?php echo htmlspecialchars($currentUser['email']); ?>)</small>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="logout.php" class="auth-form">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(csrfToken()); ?>">
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        Yes, Sign Out
                    </button>
                    <a href="javascript:history.back()" class="btn btn-secondary">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>