<?php
/**
 * Password Reset Request Page
 * Handles password reset requests with email validation and security features
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('/dashboard/' . $_SESSION['user_role'] . '.php');
}

$errors = [];
$email = '';
$resetSent = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $errors['general'] = 'Security token mismatch. Please try again.';
    } else {
        $email = sanitizeEmail($_POST['email'] ?? '');
        
        // Validate email
        if (empty($email)) {
            $errors['email'] = 'Email is required';
        } elseif (!validateEmail($email)) {
            $errors['email'] = 'Please enter a valid email address';
        }
        
        // Process password reset request if no validation errors
        if (empty($errors)) {
            $result = processPasswordResetRequest($email);
            
            if ($result['success']) {
                $resetSent = true;
                logSecurityEvent('password_reset_requested', [
                    'email' => $email,
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
            } else {
                // Always show success message for security (don't reveal if email exists)
                $resetSent = true;
            }
        }
    }
}

/**
 * Process password reset request
 */
function processPasswordResetRequest($email) {
    global $auth;
    
    try {
        $db = new DatabaseQuery();
        
        // Check if user exists
        $user = $db->selectOne('users', ['email' => $email]);
        
        if (!$user) {
            // Don't reveal that email doesn't exist for security
            return ['success' => true];
        }
        
        // Check if user account is active
        if ($user['status'] !== 'active') {
            return ['success' => true]; // Don't reveal account status
        }
        
        // Generate reset token
        $resetToken = generateSecureToken(32);
        $resetExpiry = date('Y-m-d H:i:s', strtotime('+1 hour')); // Token expires in 1 hour
        
        // Store reset token in database (you might want to create a password_resets table)
        // For now, we'll store it in the session for demonstration
        $_SESSION['password_reset_tokens'][$resetToken] = [
            'user_id' => $user['id'],
            'email' => $email,
            'expires' => $resetExpiry,
            'used' => false
        ];
        
        // In a real application, you would send an email here
        // For demonstration, we'll log the reset link
        $resetLink = SITE_URL . '/reset-password.php?token=' . $resetToken;
        
        logSecurityEvent('password_reset_token_generated', [
            'user_id' => $user['id'],
            'email' => $email,
            'token' => $resetToken,
            'expires' => $resetExpiry,
            'reset_link' => $resetLink // In production, don't log the actual link
        ]);
        
        // TODO: Send email with reset link
        // sendPasswordResetEmail($email, $resetLink);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        logError('Password reset request error: ' . $e->getMessage(), ['email' => $email]);
        return ['success' => false];
    }
}

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-card">
            <?php if (!$resetSent): ?>
                <div class="auth-header">
                    <h1>Forgot Password</h1>
                    <p>Enter your email address and we'll send you a link to reset your password.</p>
                </div>
                
                <!-- Flash Messages -->
                <?php if (!empty($flashMessages)): ?>
                    <div class="flash-messages">
                        <?php foreach ($flashMessages as $message): ?>
                            <div class="alert alert-<?php echo htmlspecialchars($message['type']); ?>">
                                <?php echo htmlspecialchars($message['message']); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <!-- General Error Message -->
                <?php if (isset($errors['general'])): ?>
                    <div class="alert alert-error">
                        <?php echo htmlspecialchars($errors['general']); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="forgot-password.php" class="auth-form" novalidate>
                    <!-- CSRF Token -->
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(csrfToken()); ?>">
                    
                    <!-- Email Field -->
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            value="<?php echo htmlspecialchars($email); ?>"
                            class="form-control <?php echo isset($errors['email']) ? 'error' : ''; ?>"
                            placeholder="Enter your email address"
                            required
                            autocomplete="email"
                        >
                        <?php if (isset($errors['email'])): ?>
                            <div class="error-message"><?php echo htmlspecialchars($errors['email']); ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary btn-full">
                        Send Reset Link
                    </button>
                </form>
                
                <!-- Back to Login Link -->
                <div class="auth-footer">
                    <p><a href="login.php">← Back to Login</a></p>
                </div>
            <?php else: ?>
                <!-- Success Message -->
                <div class="auth-header">
                    <h1>Check Your Email</h1>
                    <div class="success-icon">✓</div>
                </div>
                
                <div class="reset-success">
                    <p>If an account with that email address exists, we've sent you a password reset link.</p>
                    <p>Please check your email and follow the instructions to reset your password.</p>
                    
                    <div class="reset-info">
                        <h3>Didn't receive the email?</h3>
                        <ul>
                            <li>Check your spam or junk folder</li>
                            <li>Make sure you entered the correct email address</li>
                            <li>The link will expire in 1 hour for security</li>
                        </ul>
                    </div>
                    
                    <div class="form-actions">
                        <a href="forgot-password.php" class="btn btn-secondary">
                            Try Again
                        </a>
                        <a href="login.php" class="btn btn-primary">
                            Back to Login
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- JavaScript for form validation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.auth-form');
            if (!form) return;
            
            const emailField = document.getElementById('email');
            
            // Real-time email validation
            emailField.addEventListener('blur', function() {
                const email = this.value.trim();
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                
                if (email && !emailRegex.test(email)) {
                    this.classList.add('error');
                    showFieldError(this, 'Please enter a valid email address');
                } else {
                    this.classList.remove('error');
                    hideFieldError(this);
                }
            });
            
            // Form submission validation
            form.addEventListener('submit', function(e) {
                const email = emailField.value.trim();
                
                if (!email) {
                    showFieldError(emailField, 'Email is required');
                    e.preventDefault();
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                    showFieldError(emailField, 'Please enter a valid email address');
                    e.preventDefault();
                } else {
                    hideFieldError(emailField);
                }
            });
            
            function showFieldError(field, message) {
                field.classList.add('error');
                
                // Remove existing error message
                const existingError = field.parentNode.querySelector('.error-message');
                if (existingError) {
                    existingError.remove();
                }
                
                // Add new error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }
            
            function hideFieldError(field) {
                field.classList.remove('error');
                const errorMessage = field.parentNode.querySelector('.error-message');
                if (errorMessage) {
                    errorMessage.remove();
                }
            }
        });
    </script>
</body>
</html>