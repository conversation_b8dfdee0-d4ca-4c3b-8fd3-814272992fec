<?php
/**
 * Password Reset Page
 * Handles password reset with token validation and new password setting
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('/dashboard/' . $_SESSION['user_role'] . '.php');
}

$errors = [];
$token = $_GET['token'] ?? ''; 
$tokenValid = false;
$resetComplete = false;
$userEmail = '';

// Validate reset token
if (!empty($token)) {
    $tokenData = validateResetToken($token);
    if ($tokenData) {
        $tokenValid = true;
        $userEmail = $tokenData['email'];
    } else {
        $errors['general'] = 'Invalid or expired reset token. Please request a new password reset.';
    }
} else {
    $errors['general'] = 'No reset token provided. Please request a new password reset.';
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $tokenValid) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $errors['general'] = 'Security token mismatch. Please try again.';
    } else {
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validate password
        if (empty($password)) {
            $errors['password'] = 'Password is required';
        } elseif (!validatePassword($password)) {
            $errors['password'] = 'Password must be at least 8 characters with uppercase, lowercase, and number';
        }
        
        // Validate password confirmation
        if (empty($confirmPassword)) {
            $errors['confirm_password'] = 'Password confirmation is required';
        } elseif ($password !== $confirmPassword) {
            $errors['confirm_password'] = 'Passwords do not match';
        }
        
        // Process password reset if no validation errors
        if (empty($errors)) {
            $result = resetUserPassword($token, $password);
            
            if ($result['success']) {
                $resetComplete = true;
                
                logSecurityEvent('password_reset_completed', [
                    'user_id' => $result['user_id'],
                    'email' => $userEmail
                ]);
                
                setFlashMessage('success', 'Your password has been reset successfully. You can now log in with your new password.');
            } else {
                $errors['general'] = $result['message'];
            }
        }
    }
}

/**
 * Validate reset token
 */
function validateResetToken($token) {
    // Check if token exists in session (in production, this would be in database)
    if (!isset($_SESSION['password_reset_tokens'][$token])) {
        return false;
    }
    
    $tokenData = $_SESSION['password_reset_tokens'][$token];
    
    // Check if token has expired
    if (strtotime($tokenData['expires']) < time()) {
        unset($_SESSION['password_reset_tokens'][$token]);
        return false;
    }
    
    // Check if token has already been used
    if ($tokenData['used']) {
        return false;
    }
    
    return $tokenData;
}

/**
 * Reset user password
 */
function resetUserPassword($token, $newPassword) {
    global $auth;
    
    try {
        $tokenData = validateResetToken($token);
        if (!$tokenData) {
            return [
                'success' => false,
                'message' => 'Invalid or expired reset token'
            ];
        }
        
        $db = new DatabaseQuery();
        
        // Get user
        $user = $db->selectOne('users', ['id' => $tokenData['user_id']]);
        if (!$user) {
            return [
                'success' => false,
                'message' => 'User not found'
            ];
        }
        
        // Hash new password
        $passwordHash = $auth->hashPassword($newPassword);
        
        // Update user password
        $updated = $db->update('users', 
            [
                'password_hash' => $passwordHash,
                'updated_at' => date('Y-m-d H:i:s')
            ], 
            ['id' => $user['id']]
        );
        
        if ($updated) {
            // Mark token as used
            $_SESSION['password_reset_tokens'][$token]['used'] = true;
            
            return [
                'success' => true,
                'user_id' => $user['id']
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to update password. Please try again.'
            ];
        }
        
    } catch (Exception $e) {
        logError('Password reset error: ' . $e->getMessage(), ['token' => $token]);
        return [
            'success' => false,
            'message' => 'Password reset failed. Please try again.'
        ];
    }
}

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Beauty Platform</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-card">
            <?php if ($resetComplete): ?>
                <!-- Success Message -->
                <div class="auth-header">
                    <h1>Password Reset Complete</h1>
                    <div class="success-icon">✓</div>
                </div>
                
                <div class="reset-success">
                    <p>Your password has been successfully reset!</p>
                    <p>You can now log in with your new password.</p>
                    
                    <div class="form-actions">
                        <a href="login.php" class="btn btn-primary btn-full">
                            Go to Login
                        </a>
                    </div>
                </div>
            <?php elseif ($tokenValid): ?>
                <!-- Reset Password Form -->
                <div class="auth-header">
                    <h1>Reset Password</h1>
                    <p>Enter your new password below</p>
                    <?php if ($userEmail): ?>
                        <small>Resetting password for: <?php echo htmlspecialchars($userEmail); ?></small>
                    <?php endif; ?>
                </div>
                
                <!-- Flash Messages -->
                <?php if (!empty($flashMessages)): ?>
                    <div class="flash-messages">
                        <?php foreach ($flashMessages as $message): ?>
                            <div class="alert alert-<?php echo htmlspecialchars($message['type']); ?>">
                                <?php echo htmlspecialchars($message['message']); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <!-- General Error Message -->
                <?php if (isset($errors['general'])): ?>
                    <div class="alert alert-error">
                        <?php echo htmlspecialchars($errors['general']); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="reset-password.php?token=<?php echo htmlspecialchars($token); ?>" class="auth-form" novalidate>
                    <!-- CSRF Token -->
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(csrfToken()); ?>">
                    
                    <!-- Password Field -->
                    <div class="form-group">
                        <label for="password">New Password</label>
                        <div class="password-input-wrapper">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-control <?php echo isset($errors['password']) ? 'error' : ''; ?>"
                                placeholder="Enter your new password"
                                required
                                autocomplete="new-password"
                            >
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <span class="show-text">Show</span>
                                <span class="hide-text" style="display: none;">Hide</span>
                            </button>
                        </div>
                        <div class="password-requirements">
                            <small>Password must be at least 8 characters with uppercase, lowercase, and number</small>
                        </div>
                        <?php if (isset($errors['password'])): ?>
                            <div class="error-message"><?php echo htmlspecialchars($errors['password']); ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Confirm Password Field -->
                    <div class="form-group">
                        <label for="confirm_password">Confirm New Password</label>
                        <div class="password-input-wrapper">
                            <input 
                                type="password" 
                                id="confirm_password" 
                                name="confirm_password" 
                                class="form-control <?php echo isset($errors['confirm_password']) ? 'error' : ''; ?>"
                                placeholder="Confirm your new password"
                                required
                                autocomplete="new-password"
                            >
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                <span class="show-text">Show</span>
                                <span class="hide-text" style="display: none;">Hide</span>
                            </button>
                        </div>
                        <?php if (isset($errors['confirm_password'])): ?>
                            <div class="error-message"><?php echo htmlspecialchars($errors['confirm_password']); ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary btn-full">
                        Reset Password
                    </button>
                </form>
            <?php else: ?>
                <!-- Invalid Token -->
                <div class="auth-header">
                    <h1>Invalid Reset Link</h1>
                    <div class="error-icon">✗</div>
                </div>
                
                <div class="reset-error">
                    <?php if (isset($errors['general'])): ?>
                        <div class="alert alert-error">
                            <?php echo htmlspecialchars($errors['general']); ?>
                        </div>
                    <?php endif; ?>
                    
                    <p>This password reset link is invalid or has expired.</p>
                    <p>Please request a new password reset to continue.</p>
                    
                    <div class="form-actions">
                        <a href="forgot-password.php" class="btn btn-primary">
                            Request New Reset
                        </a>
                        <a href="login.php" class="btn btn-secondary">
                            Back to Login
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- JavaScript for password toggle and validation -->
    <script>
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleButton = passwordField.nextElementSibling;
            const showText = toggleButton.querySelector('.show-text');
            const hideText = toggleButton.querySelector('.hide-text');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                showText.style.display = 'none';
                hideText.style.display = 'inline';
            } else {
                passwordField.type = 'password';
                showText.style.display = 'inline';
                hideText.style.display = 'none';
            }
        }
        
        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.auth-form');
            if (!form) return;
            
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('confirm_password');
            
            // Password strength validation
            passwordField.addEventListener('input', function() {
                const password = this.value;
                const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
                
                if (password && !passwordRegex.test(password)) {
                    this.classList.add('error');
                } else {
                    this.classList.remove('error');
                }
                
                // Also check confirm password if it has a value
                if (confirmPasswordField.value) {
                    validatePasswordMatch();
                }
            });
            
            // Password confirmation validation
            confirmPasswordField.addEventListener('input', validatePasswordMatch);
            
            function validatePasswordMatch() {
                const password = passwordField.value;
                const confirmPassword = confirmPasswordField.value;
                
                if (confirmPassword && password !== confirmPassword) {
                    confirmPasswordField.classList.add('error');
                    showFieldError(confirmPasswordField, 'Passwords do not match');
                } else {
                    confirmPasswordField.classList.remove('error');
                    hideFieldError(confirmPasswordField);
                }
            }
            
            // Form submission validation
            form.addEventListener('submit', function(e) {
                let hasErrors = false;
                
                // Validate password
                const password = passwordField.value;
                const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
                
                if (!password) {
                    showFieldError(passwordField, 'Password is required');
                    hasErrors = true;
                } else if (!passwordRegex.test(password)) {
                    showFieldError(passwordField, 'Password must be at least 8 characters with uppercase, lowercase, and number');
                    hasErrors = true;
                } else {
                    hideFieldError(passwordField);
                }
                
                // Validate password confirmation
                const confirmPassword = confirmPasswordField.value;
                if (!confirmPassword) {
                    showFieldError(confirmPasswordField, 'Password confirmation is required');
                    hasErrors = true;
                } else if (password !== confirmPassword) {
                    showFieldError(confirmPasswordField, 'Passwords do not match');
                    hasErrors = true;
                } else {
                    hideFieldError(confirmPasswordField);
                }
                
                if (hasErrors) {
                    e.preventDefault();
                }
            });
            
            function showFieldError(field, message) {
                field.classList.add('error');
                
                // Remove existing error message
                const existingError = field.parentNode.querySelector('.error-message');
                if (existingError) {
                    existingError.remove();
                }
                
                // Add new error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }
            
            function hideFieldError(field) {
                field.classList.remove('error');
                const errorMessage = field.parentNode.querySelector('.error-message');
                if (errorMessage) {
                    errorMessage.remove();
                }
            }
        });
    </script>
</body>
</html>