<?php
/**
 * Database Connection Class
 * Handles PDO connection and basic database operations
 */

require_once 'config.php';

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed. Please try again later.");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // Prevent cloning of the instance
    private function __clone() {}
    
    // Prevent unserialization of the instance
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * Database Query Helper Class
 * Provides CRUD operations with prepared statements
 */
class DatabaseQuery {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Select records from database
     */
    public function select($table, $conditions = [], $joins = [], $orderBy = '', $limit = '', $columns = '*') {
        try {
            $sql = "SELECT {$columns} FROM " . $table;
            
            // Add joins
            if (!empty($joins)) {
                foreach ($joins as $join) {
                    $sql .= " " . $join;
                }
            }
            
            // Add conditions
            $params = [];
            if (!empty($conditions)) {
                $whereClause = [];
                foreach ($conditions as $key => $value) {
                    $paramName = 'param_' . count($params);
                    $whereClause[] = "{$key} = :{$paramName}";
                    $params[$paramName] = $value;
                }
                $sql .= " WHERE " . implode(' AND ', $whereClause);
            }
            
            // Add order by
            if (!empty($orderBy)) {
                $sql .= " ORDER BY " . $orderBy;
            }
            
            // Add limit
            if (!empty($limit)) {
                $sql .= " LIMIT " . $limit;
            }
            
            $stmt = $this->db->prepare($sql);
            
            // Bind parameters
            foreach ($params as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }
            
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Select query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Database query failed");
        }
    }
    
    /**
     * Select single record from database
     */
    public function selectOne($table, $conditions = [], $joins = [], $columns = '*') {
        $results = $this->select($table, $conditions, $joins, '', '1', $columns);
        return !empty($results) ? $results[0] : null;
    }
    
    /**
     * Count records in database
     */
    public function count($table, $conditions = []) {
        try {
            $sql = "SELECT COUNT(*) as count FROM " . $table;
            
            $params = [];
            if (!empty($conditions)) {
                $whereClause = [];
                foreach ($conditions as $key => $value) {
                    $paramName = 'param_' . count($params);
                    $whereClause[] = "{$key} = :{$paramName}";
                    $params[$paramName] = $value;
                }
                $sql .= " WHERE " . implode(' AND ', $whereClause);
            }
            
            $stmt = $this->db->prepare($sql);
            
            foreach ($params as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }
            
            $stmt->execute();
            $result = $stmt->fetch();
            return (int)$result['count'];
        } catch (PDOException $e) {
            error_log("Count query failed: " . $e->getMessage());
            throw new Exception("Database count failed");
        }
    }
    
    /**
     * Insert record into database
     */
    public function insert($table, $data) {
        try {
            $columns = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            
            $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
            $stmt = $this->db->prepare($sql);
            
            foreach ($data as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }
            
            $stmt->execute();
            return $this->db->lastInsertId();
        } catch (PDOException $e) {
            error_log("Insert query failed: " . $e->getMessage());
            throw new Exception("Database insert failed");
        }
    }
    
    /**
     * Update records in database
     */
    public function update($table, $data, $conditions) {
        try {
            $setClause = [];
            foreach ($data as $key => $value) {
                $setClause[] = "{$key} = :{$key}";
            }
            
            $whereClause = [];
            foreach ($conditions as $key => $value) {
                $whereClause[] = "{$key} = :where_{$key}";
            }
            
            $sql = "UPDATE {$table} SET " . implode(', ', $setClause) . 
                   " WHERE " . implode(' AND ', $whereClause);
            
            $stmt = $this->db->prepare($sql);
            
            // Bind data parameters
            foreach ($data as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }
            
            // Bind condition parameters
            foreach ($conditions as $key => $value) {
                $stmt->bindValue(':where_' . $key, $value);
            }
            
            $stmt->execute();
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Update query failed: " . $e->getMessage());
            throw new Exception("Database update failed");
        }
    }
    
    /**
     * Delete records from database
     */
    public function delete($table, $conditions) {
        try {
            $whereClause = [];
            foreach ($conditions as $key => $value) {
                $whereClause[] = "{$key} = :{$key}";
            }
            
            $sql = "DELETE FROM {$table} WHERE " . implode(' AND ', $whereClause);
            $stmt = $this->db->prepare($sql);
            
            foreach ($conditions as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }
            
            $stmt->execute();
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Delete query failed: " . $e->getMessage());
            throw new Exception("Database delete failed");
        }
    }
    
    /**
     * Execute custom query
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->db->prepare($sql);
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Custom query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Database query failed");
        }
    }
    
    /**
     * Execute custom query and return single result
     */
    public function queryOne($sql, $params = []) {
        $results = $this->query($sql, $params);
        return !empty($results) ? $results[0] : null;
    }
    
    /**
     * Begin database transaction
     */
    public function beginTransaction() {
        try {
            return $this->db->beginTransaction();
        } catch (PDOException $e) {
            error_log("Begin transaction failed: " . $e->getMessage());
            throw new Exception("Transaction start failed");
        }
    }
    
    /**
     * Commit database transaction
     */
    public function commit() {
        try {
            return $this->db->commit();
        } catch (PDOException $e) {
            error_log("Commit transaction failed: " . $e->getMessage());
            throw new Exception("Transaction commit failed");
        }
    }
    
    /**
     * Rollback database transaction
     */
    public function rollback() {
        try {
            return $this->db->rollback();
        } catch (PDOException $e) {
            error_log("Rollback transaction failed: " . $e->getMessage());
            throw new Exception("Transaction rollback failed");
        }
    }
    
    /**
     * Check if record exists
     */
    public function exists($table, $conditions) {
        return $this->count($table, $conditions) > 0;
    }
    
    /**
     * Get last insert ID
     */
    public function getLastInsertId() {
        return $this->db->lastInsertId();
    }
}
?>