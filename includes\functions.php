<?php

/**
 * Utility Functions for Beauty Platform
 * Contains input validation, sanitization, and helper functions
 */

/**
 * Input Validation Functions
 */

/**
 * Validate email address
 */
function validateEmail($email)
{
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate password strength
 */
function validatePassword($password)
{
    // At least 8 characters, one uppercase, one lowercase, one number
    return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/', $password);
}

/**
 * Validate phone number (basic international format)
 */
function validatePhone($phone)
{
    // Remove all non-digit characters for validation
    $cleanPhone = preg_replace('/[^0-9]/', '', $phone);
    return strlen($cleanPhone) >= 10 && strlen($cleanPhone) <= 15;
}

/**
 * Validate name (letters, spaces, hyphens, apostrophes only)
 */
function validateName($name)
{
    return preg_match('/^[a-zA-Z\s\-\']{2,50}$/', trim($name));
}

/**
 * Validate numeric ID
 */
function validateId($id)
{
    return is_numeric($id) && $id > 0;
}

/**
 * Validate date format (YYYY-MM-DD)
 */
function validateDate($date)
{
    $d = DateTime::createFromFormat('Y-m-d', $date);
    return $d && $d->format('Y-m-d') === $date;
}

/**
 * Validate time format (HH:MM)
 */
function validateTime($time)
{
    return preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time);
}

/**
 * Validate price (positive decimal with up to 2 decimal places)
 */
function validatePrice($price)
{
    return is_numeric($price) && $price >= 0 && preg_match('/^\d+(\.\d{1,2})?$/', $price);
}

/**
 * Validate rating (1-5 integer)
 */
function validateRating($rating)
{
    return is_numeric($rating) && $rating >= 1 && $rating <= 5 && $rating == (int)$rating;
}

/**
 * Input Sanitization Functions
 */

/**
 * Sanitize string input
 */
function sanitizeString($input)
{
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Sanitize email input
 */
function sanitizeEmail($email)
{
    return filter_var(trim($email), FILTER_SANITIZE_EMAIL);
}

/**
 * Sanitize phone number (keep only digits, spaces, hyphens, parentheses, plus)
 */
function sanitizePhone($phone)
{
    return preg_replace('/[^0-9\s\-\(\)\+]/', '', trim($phone));
}

/**
 * Sanitize numeric input
 */
function sanitizeNumber($number)
{
    return filter_var($number, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
}

/**
 * Sanitize integer input
 */
function sanitizeInt($number)
{
    return filter_var($number, FILTER_SANITIZE_NUMBER_INT);
}

/**
 * Sanitize URL input
 */
function sanitizeUrl($url)
{
    return filter_var(trim($url), FILTER_SANITIZE_URL);
}

/**
 * Sanitize textarea/long text input
 */
function sanitizeText($text)
{
    // Remove script tags and other dangerous HTML
    $text = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $text);
    return htmlspecialchars(trim($text), ENT_QUOTES, 'UTF-8');
}

/**
 * Security Helper Functions
 */

/**
 * Generate secure random token
 */
function generateSecureToken($length = 32)
{
    return bin2hex(random_bytes($length));
}

/**
 * Enhanced XSS Protection Functions
 */

/**
 * Escape output for HTML context
 */
function escapeHtml($string)
{
    return htmlspecialchars($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');
}

/**
 * Escape output for HTML attribute context
 */
function escapeHtmlAttr($string)
{
    return htmlspecialchars($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');
}

/**
 * Escape output for JavaScript context
 */
function escapeJs($string)
{
    return json_encode($string, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
}

/**
 * Escape output for URL context
 */
function escapeUrl($string)
{
    return rawurlencode($string);
}

/**
 * Clean HTML content (remove dangerous tags and attributes)
 */
function cleanHtml($html)
{
    // Remove script tags and their content
    $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
    
    // Remove dangerous event handlers
    $html = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $html);
    
    // Remove javascript: URLs
    $html = preg_replace('/javascript\s*:/i', '', $html);
    
    // Remove data: URLs (except images)
    $html = preg_replace('/data\s*:(?!image\/)/i', '', $html);
    
    return $html;
}

/**
 * Enhanced Input Validation Functions
 */

/**
 * Validate and sanitize file upload
 */
function validateFileUpload($file, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'], $maxSize = null)
{
    $errors = [];
    
    if (!isset($file['error']) || is_array($file['error'])) {
        $errors[] = 'Invalid file upload';
        return ['valid' => false, 'errors' => $errors];
    }
    
    // Check for upload errors
    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            $errors[] = 'No file was uploaded';
            break;
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            $errors[] = 'File is too large';
            break;
        default:
            $errors[] = 'Unknown upload error';
            break;
    }
    
    if (!empty($errors)) {
        return ['valid' => false, 'errors' => $errors];
    }
    
    // Check file size
    $maxSize = $maxSize ?? MAX_UPLOAD_SIZE;
    if ($file['size'] > $maxSize) {
        $errors[] = 'File size exceeds maximum allowed size';
    }
    
    // Check file type by extension
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, $allowedTypes)) {
        $errors[] = 'File type not allowed';
    }
    
    // Check MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    $allowedMimes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif'
    ];
    
    if (isset($allowedMimes[$fileExtension]) && $mimeType !== $allowedMimes[$fileExtension]) {
        $errors[] = 'File content does not match extension';
    }
    
    // Additional security check for images
    if (in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif'])) {
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            $errors[] = 'Invalid image file';
        }
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'extension' => $fileExtension,
        'mime_type' => $mimeType,
        'size' => $file['size']
    ];
}

/**
 * Rate Limiting Functions
 */

/**
 * Check rate limit for specific action
 */
function checkRateLimit($action, $identifier, $maxAttempts = 5, $timeWindow = 900)
{
    $cacheKey = "rate_limit_{$action}_" . md5($identifier);
    
    if (!isset($_SESSION[$cacheKey])) {
        $_SESSION[$cacheKey] = [
            'attempts' => 0,
            'first_attempt' => time()
        ];
    }
    
    $data = $_SESSION[$cacheKey];
    
    // Reset if time window has passed
    if (time() - $data['first_attempt'] > $timeWindow) {
        $_SESSION[$cacheKey] = [
            'attempts' => 0,
            'first_attempt' => time()
        ];
        return true;
    }
    
    // Check if limit exceeded
    return $data['attempts'] < $maxAttempts;
}

/**
 * Record rate limit attempt
 */
function recordRateLimitAttempt($action, $identifier)
{
    $cacheKey = "rate_limit_{$action}_" . md5($identifier);
    
    if (!isset($_SESSION[$cacheKey])) {
        $_SESSION[$cacheKey] = [
            'attempts' => 0,
            'first_attempt' => time()
        ];
    }
    
    $_SESSION[$cacheKey]['attempts']++;
}

/**
 * Clear rate limit data
 */
function clearRateLimit($action, $identifier)
{
    $cacheKey = "rate_limit_{$action}_" . md5($identifier);
    unset($_SESSION[$cacheKey]);
}

/**
 * Security Headers Functions
 */

/**
 * Set security headers
 */
function setSecurityHeaders()
{
    // Prevent XSS attacks
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    
    // Content Security Policy
    $csp = "default-src 'self'; " .
           "script-src 'self' 'unsafe-inline'; " .
           "style-src 'self' 'unsafe-inline'; " .
           "img-src 'self' data: https:; " .
           "font-src 'self'; " .
           "connect-src 'self'; " .
           "frame-ancestors 'none';";
    header("Content-Security-Policy: {$csp}");
    
    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // HTTPS enforcement (uncomment in production with HTTPS)
    // header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
}

/**
 * Enhanced Error Handling Functions
 */

/**
 * Handle application errors gracefully
 */
function handleError($errno, $errstr, $errfile, $errline)
{
    // Don't handle errors that are suppressed with @
    if (!(error_reporting() & $errno)) {
        return false;
    }
    
    $errorTypes = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Notice',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];
    
    $errorType = $errorTypes[$errno] ?? 'Unknown Error';
    
    // Log the error
    $logMessage = sprintf(
        "[%s] %s: %s in %s on line %d",
        date('Y-m-d H:i:s'),
        $errorType,
        $errstr,
        $errfile,
        $errline
    );
    
    logErrorWithRotation($logMessage, [
        'type' => $errorType,
        'file' => $errfile,
        'line' => $errline,
        'user_id' => getCurrentUserId(),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    // For fatal errors, show error page
    if (in_array($errno, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
        showErrorPage(500, 'Internal Server Error');
        exit;
    }
    
    return true;
}

/**
 * Handle uncaught exceptions
 */
function handleException($exception)
{
    $logMessage = sprintf(
        "[%s] Uncaught Exception: %s in %s on line %d\nStack trace:\n%s",
        date('Y-m-d H:i:s'),
        $exception->getMessage(),
        $exception->getFile(),
        $exception->getLine(),
        $exception->getTraceAsString()
    );
    
    logErrorWithRotation($logMessage, [
        'type' => 'Exception',
        'class' => get_class($exception),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'user_id' => getCurrentUserId(),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    showErrorPage(500, 'Internal Server Error');
}

/**
 * Show error page
 */
function showErrorPage($code = 500, $message = 'Internal Server Error')
{
    http_response_code($code);
    
    $errorPages = [
        404 => '404.php',
        500 => '500.php'
    ];
    
    $errorPage = $errorPages[$code] ?? '500.php';
    
    if (file_exists($errorPage)) {
        include $errorPage;
    } else {
        // Fallback error page
        echo "<!DOCTYPE html>
<html>
<head>
    <title>Error {$code}</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
        .error-container { max-width: 500px; margin: 0 auto; }
        h1 { color: #e74c3c; }
    </style>
</head>
<body>
    <div class='error-container'>
        <h1>Error {$code}</h1>
        <p>" . escapeHtml($message) . "</p>
        <a href='/'>Return to Home</a>
    </div>
</body>
</html>";
    }
}

/**
 * Enhanced logging with rotation
 */
function logErrorWithRotation($message, $context = [], $logFile = 'error.log')
{
    $logsDir = __DIR__ . '/../logs';
    $logPath = $logsDir . '/' . $logFile;
    
    // Ensure logs directory exists
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }
    
    // Check if log rotation is needed (if file is larger than 10MB)
    if (file_exists($logPath) && filesize($logPath) > 10 * 1024 * 1024) {
        $rotatedPath = $logsDir . '/' . pathinfo($logFile, PATHINFO_FILENAME) . '_' . date('Y-m-d_H-i-s') . '.log';
        rename($logPath, $rotatedPath);
        
        // Keep only last 10 rotated files
        $rotatedFiles = glob($logsDir . '/' . pathinfo($logFile, PATHINFO_FILENAME) . '_*.log');
        if (count($rotatedFiles) > 10) {
            usort($rotatedFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // Remove oldest files
            for ($i = 0; $i < count($rotatedFiles) - 10; $i++) {
                unlink($rotatedFiles[$i]);
            }
        }
    }
    
    $logMessage = date('Y-m-d H:i:s') . ' - ' . $message;
    if (!empty($context)) {
        $logMessage .= ' - Context: ' . json_encode($context, JSON_UNESCAPED_SLASHES);
    }
    
    error_log($logMessage . PHP_EOL, 3, $logPath);
}

/**
 * Initialize security measures
 */
function initializeSecurity()
{
    // Set error handlers
    set_error_handler('handleError');
    set_exception_handler('handleException');
    
    // Set security headers
    setSecurityHeaders();
    
    // Ensure logs directory exists
    ensureLogsDirectory();
}

/**
 * Data Processing Helper Functions
 */

/**
 * Format price for display
 */
function formatPrice($price, $currency = '$')
{
    return $currency . number_format($price, 2);
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'M j, Y')
{
    return date($format, strtotime($date));
}

/**
 * Format time for display
 */
function formatTime($time, $format = 'g:i A')
{
    return date($format, strtotime($time));
}

/**
 * Calculate time slots between start and end time
 */
function generateTimeSlots($startTime, $endTime, $interval = 30)
{
    $slots = [];
    $start = new DateTime($startTime);
    $end = new DateTime($endTime);

    while ($start < $end) {
        $slots[] = $start->format('H:i');
        $start->add(new DateInterval('PT' . $interval . 'M'));
    }

    return $slots;
}

/**
 * Check if user is admin
 */
function isAdmin()
{
    return hasRole('admin');
}

/**
 * Check if user is shop owner
 */
function isShopOwner()
{
    return hasRole('shop_owner');
}

/**
 * Check if user is customer
 */
function isCustomer()
{
    return hasRole('customer');
}

/**
 * Get current user ID
 */
function getCurrentUserId()
{
    return $_SESSION['user_id'] ?? null;
}

/**
 * Redirect to URL
 */
function redirect($url)
{
    header("Location: $url");
    exit();
}

/**
 * Set flash message
 */
function setFlashMessage($type, $message)
{
    $_SESSION['flash_messages'][] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Get and clear flash messages
 */
function getFlashMessages()
{
    $messages = $_SESSION['flash_messages'] ?? [];
    unset($_SESSION['flash_messages']);
    return $messages;
}

/**
 * Log error message
 */
function logError($message, $context = [])
{
    $logMessage = date('Y-m-d H:i:s') . ' - ' . $message;
    if (!empty($context)) {
        $logMessage .= ' - Context: ' . json_encode($context);
    }
    error_log($logMessage . PHP_EOL, 3, __DIR__ . '/../logs/error.log');
}

/**
 * Log security event
 */
function logSecurityEvent($event, $details = [])
{
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'user_id' => getCurrentUserId(),
        'details' => $details
    ];

    $logMessage = json_encode($logData);
    error_log($logMessage . PHP_EOL, 3, __DIR__ . '/../logs/security.log');
}

/**
 * File Upload Helper Functions
 */

/**
 * Validate uploaded file
 */
function validateUploadedFile($file, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'], $maxSize = null)
{
    if (!isset($file['error']) || is_array($file['error'])) {
        return false;
    }

    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    // Check file size
    $maxSize = $maxSize ?? MAX_UPLOAD_SIZE;
    if ($file['size'] > $maxSize) {
        return false;
    }

    // Check file type
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, $allowedTypes)) {
        return false;
    }

    // Additional security check for images
    if (in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif'])) {
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return false;
        }
    }

    return true;
}

/**
 * Generate unique filename
 */
function generateUniqueFilename($originalName)
{
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    return uniqid() . '_' . time() . '.' . $extension;
}

/**
 * Array and Data Helper Functions
 */

/**
 * Get array value safely
 */
function getArrayValue($array, $key, $default = null)
{
    return isset($array[$key]) ? $array[$key] : $default;
}

/**
 * Clean array of empty values
 */
function cleanArray($array)
{
    return array_filter($array, function ($value) {
        return !empty($value) || $value === '0' || $value === 0;
    });
}

/**
 * Pagination helper
 */
function paginate($totalItems, $itemsPerPage, $currentPage = 1)
{
    $totalPages = ceil($totalItems / $itemsPerPage);
    $currentPage = max(1, min($totalPages, $currentPage));
    $offset = ($currentPage - 1) * $itemsPerPage;

    return [
        'total_items' => $totalItems,
        'items_per_page' => $itemsPerPage,
        'total_pages' => $totalPages,
        'current_page' => $currentPage,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages
    ];
}

/**
 * Working Hours Helper Functions
 */

/**
 * Parse working hours JSON
 */
function parseWorkingHours($workingHoursJson)
{
    if (empty($workingHoursJson)) {
        return [];
    }

    $hours = json_decode($workingHoursJson, true);
    return is_array($hours) ? $hours : [];
}

/**
 * Check if shop is open at specific time
 */
function isShopOpen($workingHours, $dayOfWeek, $time)
{
    $hours = parseWorkingHours($workingHours);

    if (!isset($hours[$dayOfWeek])) {
        return false;
    }

    $dayHours = $hours[$dayOfWeek];
    if (!isset($dayHours['open']) || !isset($dayHours['close'])) {
        return false;
    }

    $openTime = strtotime($dayHours['open']);
    $closeTime = strtotime($dayHours['close']);
    $checkTime = strtotime($time);

    return $checkTime >= $openTime && $checkTime <= $closeTime;
}

/**
 * Get day of week name
 */
function getDayName($dayNumber)
{
    $days = [
        0 => 'Sunday',
        1 => 'Monday',
        2 => 'Tuesday',
        3 => 'Wednesday',
        4 => 'Thursday',
        5 => 'Friday',
        6 => 'Saturday'
    ];

    return $days[$dayNumber] ?? '';
}

/**
 * Create logs directory if it doesn't exist
 */
function ensureLogsDirectory()
{
    $logsDir = __DIR__ . '/../logs';
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }
}

// Initialize security measures
initializeSecurity();